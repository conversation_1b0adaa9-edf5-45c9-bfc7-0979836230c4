<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="theme-color" content="#f6b130">
    <title>Shared Trip - {{ $tripData['isActive'] ? 'Live Tracking' : 'Trip Details' }}</title>
    
    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #f6b130;
            --primary-light: #f8d48e;
            --primary-dark: #d99618;
            --secondary-color: #2b5dd9;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #fffdf4;
            --bg-dark: #111827;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--bg-light);
            position: relative;
            color: var(--text-dark);
            line-height: 1.5;
            min-height: 100vh;
            overflow-x: hidden;
            padding-bottom: env(safe-area-inset-bottom, 0);
        }
        
        /* Creative background elements */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                linear-gradient(120deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 253, 244, 0.9) 50%, rgba(246, 177, 48, 0.08) 100%),
                linear-gradient(60deg, rgba(246, 177, 48, 0.05) 0%, rgba(255, 253, 244, 0.95) 50%, rgba(255, 255, 255, 0.9) 100%);
            z-index: -1;
        }
        
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23f6b130' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
            z-index: -1;
            opacity: 0.7;
        }
        
        /* Fullscreen map container styles */
        #fullscreen-map-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: white;
            z-index: 50;
            transition: opacity 0.3s ease;
        }

        #fullscreen-map-container.hidden {
            display: none;
        }

        #fullscreen-map {
            height: 100%;
            width: 100%;
        }

        /* Close button styles */
        #close-fullscreen-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 16px;
            right: 16px;
            z-index: 10;
            background-color: white;
            border-radius: 8px; /* Changed from 50% to 8px for rectangular shape */
            padding: 8px 12px; /* Adjusted padding for rectangular shape */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: none;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        #close-fullscreen-btn:hover {
            transform: scale(1.05);
            background-color: #f8f8f8;
        }

        #close-fullscreen-btn:active {
            transform: scale(0.95);
        }

        /* Fullscreen button styles with top-right positioning */
        #fullscreen-map-btn {
            display: flex; /* Show the button */
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease;
            position: absolute;
            top: 6px;
            right: 10px;
            z-index: 10;
            background-color: white;
            border-radius: 8px; /* Changed from 50% to 8px for rectangular shape */
            padding: 8px 12px; /* Adjusted padding for rectangular shape */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: none;
            cursor: pointer;
        }

        #fullscreen-map-btn:hover {
            transform: scale(1.05);
            background-color: #f8f8f8;
        }

        #fullscreen-map-btn:active {
            transform: scale(0.95);
        }

        /* Improved map container for mobile with positioning context for the button */
        .map-container {
            height: 40vh;
            min-height: 250px;
            width: 100%;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 4px 15px -1px rgba(0, 0, 0, 0.1), 0 2px 8px -1px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(246, 177, 48, 0.2);
            margin-bottom: 0; /* Remove bottom margin since it's part of a card now */
            position: relative;
        }

        /* Card container for the map */
        .card.relative {
            position: relative;
            margin-bottom: 1rem;
        }
        
        .driver-image {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid white;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        /* Mobile-optimized card styles */
        .card {
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 4px 15px -1px rgba(0, 0, 0, 0.05), 0 2px 8px -1px rgba(0, 0, 0, 0.03);
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            position: relative;
            margin-bottom: 1rem;
            transform: translateZ(0); /* Hardware acceleration */
            will-change: transform, box-shadow;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 18px -1px rgba(0, 0, 0, 0.1), 0 3px 10px -1px rgba(0, 0, 0, 0.05);
        }
        
        /* Subtle card accent */
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary-light), var(--primary-color), var(--primary-dark));
            opacity: 0.8;
        }
        
        /* Card section headings */
        .card h3 {
            font-size: 1.1rem;
            color: var(--text-dark);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.35rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: capitalize;
        }
        
        .status-badge.active {
            background-color: rgba(16, 185, 129, 0.1);
            color: rgb(16, 185, 129);
        }
        
        .status-badge.completed {
            background-color: rgba(79, 70, 229, 0.1);
            color: rgb(79, 70, 229);
        }
        
        .divider {
            height: 1px;
            width: 100%;
            background: linear-gradient(to right, transparent, rgba(229, 231, 235, 0.7), transparent);
            margin: 1rem 0;
        }
        
        /* Location card styles */
        .location-card {
            position: relative;
            padding-left: 2rem;
            margin-bottom: 1.5rem;
        }

        .location-card::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background-color: var(--primary-color);
        }

        .location-card.pickup::before {
            background-color: var(--primary-color);
        }

        .location-card.destination::before {
            background-color: var(--primary-dark);
        }

        .location-card.with-destination::after {
            content: '';
            position: absolute;
            left: 0.9rem;
            top: 1rem;
            width: 0.2rem;
            height: calc(100% + 0.5rem);
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-dark));
            opacity: 0.5;
        }
        
        .btn-primary {
            background: linear-gradient(to bottom, var(--primary-light), var(--primary-color));
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s;
            border: 1px solid var(--primary-color);
        }
        
        .btn-primary:hover {
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-dark));
        }
        
        /* Mobile-optimized content container */
        .content-container {
            width: 100%;
            padding: 1rem;
            position: relative;
            z-index: 1;
        }
        
        /* Improved pulse animation for live indicator */
        .pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            display: inline-block;
            height: 8px;
            width: 8px;
            border-radius: 50%;
            background-color: var(--primary-color);
            margin-right: 4px;
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
        
        /* Mobile-friendly sticky header with rounded corners and improved styling */
        .page-header {
            position: sticky;
            top: 0.5rem; /* Add some space at the top */
            z-index: 10;
            display: flex;
            align-items: center;
            padding: 0.8rem 1.2rem;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 1rem; /* Rounded corners */
            box-shadow: 0 4px 20px -5px rgba(0, 0, 0, 0.1);
            margin: 0.5rem 0.5rem 1rem 0.5rem; /* Add margin on all sides */
            border: 1px solid rgba(246, 177, 48, 0.15); /* Subtle border */
            transition: all 0.3s ease;
        }

        .page-header:hover {
            box-shadow: 0 6px 25px -5px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .page-header img {
            height: 2.5rem;
            margin-right: 1rem;
        }

        .page-header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-dark);
            background: linear-gradient(to right, var(--text-dark), var(--primary-dark));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        /* Style for the live indicator in the header */
        .page-header .inline-flex {
            padding: 0.4rem 0.8rem !important;
            border-radius: 1rem !important;
            font-weight: 600 !important;
            letter-spacing: 0.02em;
            box-shadow: 0 2px 8px -2px rgba(246, 177, 48, 0.3);
            border: 1px solid rgba(246, 177, 48, 0.2);
        }

        /* Adjust the pulse animation for better visibility */
        .pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            display: inline-block;
            height: 8px;
            width: 8px;
            border-radius: 50%;
            background-color: var(--primary-color);
            margin-right: 6px;
            box-shadow: 0 0 0 rgba(246, 177, 48, 0.4);
            animation: pulse-shadow 2s infinite;
        }

        @keyframes pulse-shadow {
            0% {
                box-shadow: 0 0 0 0 rgba(246, 177, 48, 0.4);
            }
            70% {
                box-shadow: 0 0 0 6px rgba(246, 177, 48, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(246, 177, 48, 0);
            }
        }

        /* Improved touch targets for mobile */
        .btn, button, a {
            min-height: 44px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Bottom navigation bar for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: white;
            display: flex;
            justify-content: space-around;
            padding: 0.75rem 1rem;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
            z-index: 10;
            padding-bottom: calc(0.75rem + env(safe-area-inset-bottom, 0));
        }
        
        /* Improved spacing for mobile */
        @media (max-width: 640px) {
            .p-6 {
                padding: 1rem;
            }
            
            .gap-6 {
                gap: 1rem;
            }
            
            .mb-6 {
                margin-bottom: 1rem;
            }
            
            .driver-image {
                width: 60px;
                height: 60px;
            }
            
            h2 {
                font-size: 1.125rem;
            }
            
            .text-xl {
                font-size: 1.125rem;
            }
        }

        /* Make logo clickable */
        #logo-container {
            cursor: pointer;
            transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            display: flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            margin-top: -0.25rem;
            border-radius: 0.75rem;
        }

        #logo-container:hover {
            transform: scale(1.05);
            background-color: rgba(246, 177, 48, 0.1);
        }

        #logo-container:active {
            transform: scale(0.95);
        }

        #logo-container svg {
            height: 2.5rem;
            width: auto;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        /* Add a tooltip to indicate logo is clickable when in fullscreen mode */
        body:has(#fullscreen-map-container:not(.hidden)) #logo-container::after {
            content: 'Click to exit fullscreen';
            position: absolute;
            bottom: -25px;
            left: 0;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            white-space: nowrap;
        }

        body:has(#fullscreen-map-container:not(.hidden)) #logo-container:hover::after {
            opacity: 1;
        }

        /* Style for the Filament logo in the header */
        #logo-container {
            cursor: pointer;
            transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            display: flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            margin-top: -0.25rem;
            border-radius: 0.75rem;
        }

        #logo-container svg {
            height: 2.5rem;
            width: auto;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        /* Add some spacing between the logo and text */
        .page-header #logo-container h1 {
            margin-left: 0.5rem;
        }

        /* Adjust logo size and position on mobile */
        @media (max-width: 640px) {
            .page-header {
                padding: 0.7rem 1rem;
                margin: 0.5rem 0.5rem 1rem 0.5rem;
            }
            
            #logo-container {
                padding: 0.4rem 0.6rem;
            }
            
            #logo-container svg {
                height: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Sticky Header -->
    <div class="page-header">
        <div id="logo-container" class="flex items-center cursor-pointer">
            <!-- Use the Filament logo component directly -->
            @include('filament.logo.logo_white')
        </div>
        
        <div class="flex-1"></div><!-- Spacer to push status to right -->
        
        @if($tripData['isActive'])
        <div class="flex items-center">
            <span class="inline-flex items-center rounded-full text-xs font-medium" style="background-color: rgba(246, 177, 48, 0.15); color: var(--primary-dark);">
                <span class="pulse"></span>
                Live 
            </span>
        </div>
        @else
        <div class="flex items-center">
            <span class="inline-flex items-center rounded-full text-xs font-medium" style="background-color: rgba(107, 114, 128, 0.15); color: var(--text-dark);">
                Completed Trip
            </span>
        </div>
        @endif
    </div>

    <div class="content-container">
        <!-- Map Card with fullscreen button in top right -->
        <div class="card relative">
            <div id="map" class="map-container"></div>
            
            <!-- Fullscreen button -->
            <button id="fullscreen-map-btn" class="absolute top-3 right-3 bg-white rounded-full p-2 shadow-md z-10">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                </svg>
            </button>
        </div>

        <!-- Fullscreen Map Container (hidden by default) -->
        <div id="fullscreen-map-container" class="fixed inset-0 bg-white z-50 hidden">
            <div id="fullscreen-map" class="w-full h-full"></div>
        </div>
        
        <!-- Trip Stats - Quick view for mobile -->
        <div class="grid grid-cols-2 gap-3 mb-4">
            <div class="bg-white rounded-xl p-3 shadow">
                <p class="text-xs font-medium text-gray-500">Distance</p>
                <p class="text-lg font-semibold text-gray-900">{{ number_format($trip->distance ?? 0, 1) }} km</p>
            </div>
            
            <div class="bg-white rounded-xl p-3 shadow">
                <p class="text-xs font-medium text-gray-500">
                    {{ $tripData['isActive'] ? 'ETA' : 'Duration' }}
                </p>
                <p class="text-lg font-semibold text-gray-900">{{ $tripData['etaText'] }}</p>
            </div>
        </div>
        
        <!-- Replace collapsible sections with regular sections -->
        <div class="space-y-4">
            <!-- Driver & Vehicle Info -->
            <div class="card p-4">
                <h3 class="font-semibold text-gray-900 mb-3">Driver & Vehicle</h3>
                
                <div class="mt-3">
                    <!-- Driver Info -->
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-16 h-16 rounded-full bg-gray-200 overflow-hidden">
                                @if($trip->driver && $trip->driver->user && $trip->driver->user->profile_photo_path)
                                    <img src="{{ Storage::url($trip->driver->user->profile_photo_path) }}" 
                                         alt="Driver Photo" class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full flex items-center justify-center bg-gray-300 text-gray-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                    </div>
                                @endif
                            </div>
                        </div>
                        
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">
                                {{ $trip->driver->user->name ?? 'N/A' }}
                            </p>
                            <div class="flex items-center mt-1">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        <svg class="w-4 h-4 {{ $i <= ($trip->driver->rating ?? 0) ? 'text-yellow-400' : 'text-gray-300' }}" 
                                             fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    @endfor
                                    <span class="text-xs text-gray-500 ml-1">
                                        {{ number_format($trip->driver->rating ?? 0, 1) }} ({{ $trip->driver->total_ratings ?? 0 }})
                                    </span>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">
                                {{ $trip->driver->phone ?? 'N/A' }}
                            </p>
                        </div>
                    </div>
                    
                    <!-- Vehicle Info -->
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <p class="text-xs font-medium text-gray-500 mb-2">Vehicle Information</p>
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <span class="text-gray-500">Brand:</span>
                                <span class="text-gray-900 font-medium block">{{ $trip->vehicle->vehicleModel->vehicleBrand->name_en ?? 'N/A' }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Model:</span>
                                <span class="text-gray-900 font-medium block">{{ $trip->vehicle->vehicleModel->name_en ?? 'N/A' }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Plate Number:</span>
                                <span class="text-gray-900 font-medium block">{{ $trip->vehicle->plate_number ?? 'N/A' }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Type:</span>
                                <span class="text-gray-900 font-medium block">{{ $trip->vehicle->vehicleType->name_en ?? 'Standard' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trip Route Info -->
            <div class="card p-4">
                <h3 class="font-semibold text-gray-900 mb-3">Trip Route</h3>
                
                <div class="space-y-4">
                    <!-- Pickup Location -->
                    <div class="location-card pickup with-destination">
                        <p class="text-xs font-medium text-gray-500">Pickup Location</p>
                        <p class="text-sm font-medium text-gray-900">{{ $trip->tripLocation->departure_address ?? 'N/A' }}</p>
                        @if($trip->actual_departure_time)
                        <p class="text-xs text-gray-500 mt-1">
                            {{ $trip->actual_departure_time->format('M d, Y · h:i A') }}
                        </p>
                        @endif
                    </div>
                    
                    <!-- Destination -->
                    <div class="location-card destination">
                        <p class="text-xs font-medium text-gray-500">Destination</p>
                        <p class="text-sm font-medium text-gray-900">{{ $trip->tripLocation->arrival_address ?? 'N/A' }}</p>
                        @if($trip->actual_arrival_time)
                        <p class="text-xs text-gray-500 mt-1">
                            {{ $trip->actual_arrival_time->format('M d, Y · h:i A') }}
                        </p>
                        @endif
                    </div>
                    
                    <!-- Trip Status -->
                    <div class="mt-3">
                        <p class="text-xs font-medium text-gray-500 mb-1">Trip Status</p>
                        <div class="flex items-center">
                            <span class="status-badge {{ $tripData['isActive'] ? 'active' : 'completed' }}">
                                {{ ucfirst(str_replace('_', ' ', $trip->status->value)) }}
                            </span>
                        </div>
                        
                        @if($trip->pricing_breakdown)
                        <div class="mt-3 bg-gray-50 rounded-lg p-3">
                            <p class="text-xs font-medium text-gray-500">Trip Fare</p>
                            <p class="text-base font-semibold text-gray-900">
                                {{ json_decode($trip->pricing_breakdown, true)['total'] ?? 'N/A' }} 
                                {{ json_decode($trip->pricing_breakdown, true)['currency'] ?? 'LYD' }}
                            </p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
       

    <script>
        // Load Google Maps API asynchronously with proper loading pattern
        function loadGoogleMapsAPI() {
            const script = document.createElement('script');
            script.src = "https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}&libraries=geometry&callback=initMap&loading=async";
            script.defer = true;
            script.async = true;
            document.head.appendChild(script);
        }
        
        // Call this function when the page loads
        window.addEventListener('load', loadGoogleMapsAPI);
        
        // Trip data from backend
        const tripData = @json($tripData);

        // Log tripData to console for debugging
        console.log('Trip Data:', tripData);

        let map, fullscreenMap, directionsService, directionsRenderer;
        let markers = []; // Store markers to reuse in fullscreen mode
        
        function initMap() {
            // Check if map container exists
            const mapElement = document.getElementById('map');
            if (!mapElement) {
                console.error('Map container not found!');
                return;
            }
            
            // Tripoli coordinates
            const tripoliCenter = { lat: 32.8872, lng: 13.1913 };
            
            // Initialize map with Tripoli as center and higher zoom level
            map = new google.maps.Map(mapElement, {
                center: tripoliCenter,
                zoom: 12, // Higher zoom level focused on Tripoli
                disableDefaultUI: true, // Disable all UI controls
                zoomControl: true, // Only enable zoom control
                mapTypeControl: false,
                scaleControl: false, // Disable scale control
                streetViewControl: false,
                rotateControl: false,
                fullscreenControl: false,
                keyboardShortcuts: false, // Disable keyboard shortcuts
                gestureHandling: 'greedy', // Make it easier to pan/zoom on mobile
                styles: [
                    {
                        "featureType": "administrative",
                        "elementType": "geometry",
                        "stylers": [{"visibility": "off"}]
                    },
                    {
                        "featureType": "poi",
                        "stylers": [{"visibility": "off"}]
                    },
                    {
                        "featureType": "transit",
                        "stylers": [{"visibility": "off"}]
                    },
                    {
                        // Hide Google Maps attribution and copyright notices
                        "featureType": "administrative.land_parcel",
                        "elementType": "labels",
                        "stylers": [{"visibility": "off"}]
                    }
                ]
            });
            
            // Create bounds to fit all markers
            const bounds = new google.maps.LatLngBounds();
            
            // Add markers and draw route on map
            setupMapContent(map, bounds);
            
            // Fit bounds to show all markers
            if (!bounds.isEmpty()) {
                // If we have markers in bounds
                map.fitBounds(bounds, {
                    top: 50,
                    right: 50,
                    bottom: 50,
                    left: 50
                });
            } else {
                // If no markers at all, default to Tripoli center
                map.setCenter(tripoliCenter);
                map.setZoom(12);
            }
            
            // Initialize fullscreen map with the same options
            fullscreenMap = new google.maps.Map(document.getElementById('fullscreen-map'), {
                center: map.getCenter(),
                zoom: map.getZoom(),
                disableDefaultUI: true,
                zoomControl: true,
                mapTypeControl: false,
                scaleControl: false,
                streetViewControl: false,
                rotateControl: false,
                fullscreenControl: false,
                keyboardShortcuts: false, // Disable keyboard shortcuts
                gestureHandling: 'greedy',
                styles: [
                    {
                        "featureType": "administrative",
                        "elementType": "geometry",
                        "stylers": [{"visibility": "off"}]
                    },
                    {
                        "featureType": "poi",
                        "stylers": [{"visibility": "off"}]
                    },
                    {
                        "featureType": "transit",
                        "stylers": [{"visibility": "off"}]
                    },
                    {
                        // Hide Google Maps attribution and copyright notices
                        "featureType": "administrative.land_parcel",
                        "elementType": "labels",
                        "stylers": [{"visibility": "off"}]
                    }
                ]
            });
            
            // Add fullscreen button functionality
            document.getElementById('fullscreen-map-btn').addEventListener('click', function() {
                // Show fullscreen container
                document.getElementById('fullscreen-map-container').classList.remove('hidden');
                document.body.style.overflow = 'hidden'; // Prevent scrolling
                
                // Trigger resize to ensure map renders correctly
                google.maps.event.trigger(fullscreenMap, 'resize');
                
                // Copy the same view from the small map
                fullscreenMap.setCenter(map.getCenter());
                fullscreenMap.setZoom(map.getZoom());
                
                // Setup map content on fullscreen map
                setupMapContent(fullscreenMap, new google.maps.LatLngBounds());
            });
        }
        
        function setupMapContent(targetMap, bounds) {
            // Use AdvancedMarkerElement instead of deprecated Marker
            if (google.maps.marker && google.maps.marker.AdvancedMarkerElement) {
                setupAdvancedMarkers(targetMap, bounds);
            } else {
                // Fallback to regular markers if AdvancedMarkerElement is not available
                setupLegacyMarkers(targetMap, bounds);
            }
            
            // Draw polyline with gradient if available
            if (tripData.polyline) {
                drawPolyline(targetMap, bounds);
            }
        }
        
        function setupAdvancedMarkers(targetMap, bounds) {
            // Add departure marker (pickup point)
            if (tripData.departure && tripData.departure.lat && tripData.departure.lng) {
                const departure = {
                    lat: parseFloat(tripData.departure.lat),
                    lng: parseFloat(tripData.departure.lng)
                };
                
                // Create a pin element for the marker
                const pinElement = document.createElement('div');
                pinElement.innerHTML = `
                    <img src="{{ env("APP_URL", "/") }}/icons/pickup_point.svg" 
                         style="width: 56px; height: 56px;" 
                         alt="Pickup Location">
                `;
                
                const departureMarker = new google.maps.marker.AdvancedMarkerElement({
                    map: targetMap,
                    position: departure,
                    title: 'Pickup Location',
                    content: pinElement
                });
                
                bounds.extend(departure);
            }
            
            // Add destination marker (drop off point)
            if (tripData.arrival && tripData.arrival.lat && tripData.arrival.lng) {
                const arrival = {
                    lat: parseFloat(tripData.arrival.lat),
                    lng: parseFloat(tripData.arrival.lng)
                };
                
                // Create a pin element for the marker
                const pinElement = document.createElement('div');
                pinElement.innerHTML = `
                    <img src="{{ env("APP_URL", "/") }}/icons/drop_off_point.svg" 
                         style="width: 56px; height: 56px;" 
                         alt="Destination">
                `;
                
                const arrivalMarker = new google.maps.marker.AdvancedMarkerElement({
                    map: targetMap,
                    position: arrival,
                    title: 'Destination',
                    content: pinElement
                });
                
                bounds.extend(arrival);
            }

            // Add driver location marker if available
            if (tripData.current_location && tripData.current_location.lat && tripData.current_location.lng) {
                const driverLocation = {
                    lat: parseFloat(tripData.current_location.lat),
                    lng: parseFloat(tripData.current_location.lng)
                };
                
                const driverMarker = new google.maps.marker.AdvancedMarkerElement({
                    position: driverLocation,
                    map: targetMap,
                    title: 'Driver Location'
                });
                
                bounds.extend(driverLocation);
            }
        }
        
        function setupLegacyMarkers(targetMap, bounds) {
            // Add departure marker (pickup point)
            if (tripData.departure && tripData.departure.lat && tripData.departure.lng) {
                const departure = {
                    lat: parseFloat(tripData.departure.lat),
                    lng: parseFloat(tripData.departure.lng)
                };
                
                const departureMarker = new google.maps.Marker({
                    position: departure,
                    map: targetMap,
                    icon: {
                        url: '{{ env("APP_URL", "/") }}/icons/pickup_point.svg',
                        scaledSize: new google.maps.Size(56, 56),
                        anchor: new google.maps.Point(28, 28)
                    },
                    title: 'Pickup Location'
                });
                
                bounds.extend(departure);
            }
            
            // Add destination marker (drop off point)
            if (tripData.arrival && tripData.arrival.lat && tripData.arrival.lng) {
                const arrival = {
                    lat: parseFloat(tripData.arrival.lat),
                    lng: parseFloat(tripData.arrival.lng)
                };
                
                const arrivalMarker = new google.maps.Marker({
                    position: arrival,
                    map: targetMap,
                    icon: {
                        url: '{{ env("APP_URL", "/") }}/icons/drop_off_point.svg',
                        scaledSize: new google.maps.Size(56, 56),
                        anchor: new google.maps.Point(28, 28)
                    },
                    title: 'Destination'
                });
                
                bounds.extend(arrival);
            }
            
            // Add driver location marker if available
            if (tripData.current_location && tripData.current_location.lat && tripData.current_location.lng) {
                const driverLocation = {
                    lat: parseFloat(tripData.current_location.lat),
                    lng: parseFloat(tripData.current_location.lng)
                };
                
                const driverMarker = new google.maps.Marker({
                    position: driverLocation,
                    map: targetMap,
                    title: 'Driver Location'
                });
                
                bounds.extend(driverLocation);
            }
        }
        
        function drawPolyline(targetMap, bounds) {
            try {
                const decodedPath = google.maps.geometry.encoding.decodePath(tripData.polyline);
                
                if (decodedPath && decodedPath.length > 0) {
                    // Create gradient polyline (yellow to black)
                    const totalPoints = decodedPath.length;
                    
                    if (totalPoints > 1) {
                        for (let i = 0; i < totalPoints - 1; i++) {
                            // Calculate color based on position in the path
                            const ratio = i / (totalPoints - 1);
                            const r = Math.round(246 - (246 * ratio)); // 246 -> 0 (yellow to black)
                            const g = Math.round(177 - (177 * ratio)); // 177 -> 0
                            const b = Math.round(48 - (48 * ratio));   // 48 -> 0
                            const color = `rgb(${r}, ${g}, ${b})`;
                            
                            // Create a small segment of the path with the calculated color
                            new google.maps.Polyline({
                                path: [decodedPath[i], decodedPath[i + 1]],
                                strokeColor: color,
                                strokeWeight: 5,
                                strokeOpacity: 0.7,
                                map: targetMap
                            });
                        }
                    } else {
                        // Fallback to single color if only one point
                        new google.maps.Polyline({
                            path: decodedPath,
                            strokeColor: '#f6b130', // Default to yellow (admin panel color)
                            strokeWeight: 5,
                            strokeOpacity: 0.7,
                            map: targetMap
                        });
                    }
                    
                    // Add polyline points to bounds
                    decodedPath.forEach(point => bounds.extend(point));
                }
            } catch (e) {
                console.error('Error decoding polyline:', e);
            }
        }
        
        // Handle Google Maps API errors
        window.gm_authFailure = function() {
            console.error('Google Maps authentication failed');
            document.getElementById('map').innerHTML = 
                '<div class="p-4 text-center text-red-500">Failed to load Google Maps. Please check your API key.</div>';
        };

        document.addEventListener('DOMContentLoaded', function() {
            // Add click handler to logo container to exit fullscreen
            document.getElementById('logo-container').addEventListener('click', function() {
                const fullscreenContainer = document.getElementById('fullscreen-map-container');
                if (fullscreenContainer && !fullscreenContainer.classList.contains('hidden')) {
                    fullscreenContainer.classList.add('hidden');
                    document.body.style.overflow = 'auto'; // Re-enable scrolling
                }
            });
            
            // Optimize map rendering
            const mapElement = document.getElementById('map');
            if (mapElement) {
                // Use IntersectionObserver to optimize map rendering
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // Ensure map is visible when scrolled into view
                            mapElement.style.visibility = 'visible';
                            // Trigger resize to ensure map renders correctly
                            if (map) {
                                google.maps.event.trigger(map, 'resize');
                            }
                        }
                    });
                }, { threshold: 0.1 });
                
                observer.observe(mapElement);
            }
        });
    </script>
</body>
</html>
