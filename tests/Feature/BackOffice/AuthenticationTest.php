<?php

namespace Tests\Feature\BackOffice;

use App\Filament\Pages\Auth\CustomLogin;
use App\Filament\Pages\Auth\PasswordReset\CustomResetPassword;
use App\Filament\Pages\Auth\RequestPasswordReset;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Livewire\Livewire;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin role
        Role::create(['name' => 'admin']);

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'type' => 'admin',
        ]);
        $this->admin->assignRole('admin');
    }

    /** @test */
    public function admin_can_login_with_valid_credentials()
    {
        Livewire::test(CustomLogin::class)
            ->fillForm([
                'email' => '<EMAIL>',
                'password' => 'password123',
            ])
            ->call('authenticate')
            ->assertHasNoFormErrors()
            ->assertRedirect('/');

        $this->assertAuthenticatedAs($this->admin);
    }

    /** @test */
    public function admin_cannot_login_with_invalid_email()
    {
        Livewire::test(CustomLogin::class)
            ->fillForm([
                'email' => '<EMAIL>',
                'password' => 'password123',
            ])
            ->call('authenticate')
            ->assertHasFormErrors(['email']);

        $this->assertGuest();
    }

    /** @test */
    public function admin_cannot_login_with_invalid_password()
    {
        Livewire::test(CustomLogin::class)
            ->fillForm([
                'email' => '<EMAIL>',
                'password' => 'wrongpassword',
            ])
            ->call('authenticate')
            ->assertHasFormErrors(['email']);

        $this->assertGuest();
    }

    /** @test */
    public function non_admin_user_cannot_access_admin_panel()
    {
        $regularUser = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'type' => 'passenger',
        ]);

        Livewire::test(CustomLogin::class)
            ->fillForm([
                'email' => '<EMAIL>',
                'password' => 'password123',
            ])
            ->call('authenticate')
            ->assertHasFormErrors(['email']);

        $this->assertGuest();
    }

    /** @test */
    public function admin_can_logout()
    {
        $this->actingAs($this->admin);

        $response = $this->post('/logout');

        $response->assertRedirect('/');
        $this->assertGuest();
    }

    /** @test */
    public function admin_can_request_password_reset()
    {
        \Notification::fake();

        Livewire::test(RequestPasswordReset::class)
            ->fillForm([
                'email' => '<EMAIL>',
            ])
            ->call('request')
            ->assertHasNoFormErrors();

        \Notification::assertSentTo(
            $this->admin,
            \Illuminate\Auth\Notifications\ResetPassword::class
        );
    }

    /** @test */
    public function admin_cannot_request_password_reset_with_invalid_email()
    {
        Livewire::test(RequestPasswordReset::class)
            ->fillForm([
                'email' => '<EMAIL>',
            ])
            ->call('request')
            ->assertHasFormErrors(['email']);
    }

    /** @test */
    public function admin_can_reset_password_with_valid_token()
    {
        $token = Password::createToken($this->admin);

        Livewire::test(CustomResetPassword::class, [
            'email' => $this->admin->email,
            'token' => $token,
        ])
            ->fillForm([
                'email' => $this->admin->email,
                'password' => 'newpassword123',
                'passwordConfirmation' => 'newpassword123',
            ])
            ->call('resetPassword')
            ->assertHasNoFormErrors()
            ->assertRedirect('/');

        // Verify password was changed
        $this->admin->refresh();
        $this->assertTrue(Hash::check('newpassword123', $this->admin->password));
    }

    /** @test */
    public function admin_cannot_reset_password_with_invalid_token()
    {
        Livewire::test(CustomResetPassword::class, [
            'email' => $this->admin->email,
            'token' => 'invalid-token',
        ])
            ->fillForm([
                'email' => $this->admin->email,
                'password' => 'newpassword123',
                'passwordConfirmation' => 'newpassword123',
            ])
            ->call('resetPassword')
            ->assertHasFormErrors(['email']);
    }

    /** @test */
    public function admin_cannot_reset_password_with_mismatched_passwords()
    {
        $token = Password::createToken($this->admin);

        Livewire::test(CustomResetPassword::class, [
            'email' => $this->admin->email,
            'token' => $token,
        ])
            ->fillForm([
                'email' => $this->admin->email,
                'password' => 'newpassword123',
                'passwordConfirmation' => 'differentpassword',
            ])
            ->call('resetPassword')
            ->assertHasFormErrors(['passwordConfirmation']);
    }

    /** @test */
    public function admin_can_change_password_in_profile_settings()
    {
        $this->actingAs($this->admin);

        // This test would require the profile settings page to be implemented
        // For now, we'll test the underlying functionality
        $this->admin->update([
            'password' => Hash::make('newpassword123')
        ]);

        $this->admin->refresh();
        $this->assertTrue(Hash::check('newpassword123', $this->admin->password));
    }

    /** @test */
    public function unauthenticated_user_is_redirected_to_login()
    {
        $response = $this->get('/');

        $response->assertRedirect('/login');
    }

    /** @test */
    public function authenticated_admin_can_access_dashboard()
    {
        $this->actingAs($this->admin);

        $response = $this->get('/');

        $response->assertSuccessful();
    }

    /** @test */
    public function admin_with_allowed_domain_can_access_panel()
    {
        $domainUser = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'type' => 'passenger', // Even non-admin type should work with allowed domain
        ]);

        $this->assertTrue($domainUser->canAccessPanel(app(\Filament\Panel::class)));
    }

    /** @test */
    public function admin_with_satoripop_domain_can_access_panel()
    {
        $domainUser = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'type' => 'passenger',
        ]);

        $this->assertTrue($domainUser->canAccessPanel(app(\Filament\Panel::class)));
    }

    /** @test */
    public function user_with_disallowed_domain_cannot_access_panel()
    {
        $regularUser = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'type' => 'passenger',
        ]);

        $this->assertFalse($regularUser->canAccessPanel(app(\Filament\Panel::class)));
    }

    /** @test */
    public function password_reset_link_expires_after_configured_duration()
    {
        $token = Password::createToken($this->admin);

        // Simulate token expiration by traveling forward in time
        $this->travel(config('auth.passwords.users.expire') + 1)->minutes();

        Livewire::test(CustomResetPassword::class, [
            'email' => $this->admin->email,
            'token' => $token,
        ])
            ->fillForm([
                'email' => $this->admin->email,
                'password' => 'newpassword123',
                'passwordConfirmation' => 'newpassword123',
            ])
            ->call('resetPassword')
            ->assertHasFormErrors(['email']);
    }
}
