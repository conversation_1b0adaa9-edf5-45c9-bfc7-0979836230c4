<?php

namespace Tests\Feature\BackOffice;

use App\Filament\Resources\Panel\UserResource;
use App\Models\User;
use Filament\Actions\DeleteAction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class UserManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;

    protected User $superAdmin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        Role::create(['name' => 'super_admin']);
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'user']);

        // Create super admin user
        $this->superAdmin = User::factory()->create([
            'email' => '<EMAIL>',
            'type' => 'admin',
        ]);
        $this->superAdmin->assignRole('super_admin');

        // Create regular admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'type' => 'admin',
        ]);
        $this->admin->assignRole('admin');
    }

    /** @test */
    public function admin_can_view_users_list()
    {
        // Create some back-office users
        $users = User::factory()->count(3)->create(['type' => 'admin']);
        foreach ($users as $user) {
            $user->assignRole('user');
        }

        $this->actingAs($this->admin);

        Livewire::test(UserResource\Pages\ListUsers::class)
            ->assertSuccessful()
            ->assertCanSeeTableRecords($users);
    }

    /** @test */
    public function admin_can_view_user_details()
    {
        $user = User::factory()->create([
            'type' => 'admin',
            'email' => '<EMAIL>',
        ]);
        $user->assignRole('user');

        $this->actingAs($this->admin);

        Livewire::test(UserResource\Pages\ViewUser::class, ['record' => $user->id])
            ->assertSuccessful()
            ->assertSee($user->email)
            ->assertSee($user->name)
            ->assertSee($user->phone_number);
    }

    /** @test */
    public function admin_can_create_new_user()
    {
        $this->actingAs($this->admin);

        $userData = [
            'email' => '<EMAIL>',
            'name' => 'New',
            'last_name' => 'User',
            'phone_number' => '+218912345678',
            'gender' => 'male',
            'type' => 'admin',
        ];

        Livewire::test(UserResource\Pages\CreateUser::class)
            ->fillForm($userData)
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'New',
            'last_name' => 'User',
            'type' => 'admin',
        ]);
    }

    /** @test */
    public function admin_can_edit_user_with_lower_role()
    {
        $user = User::factory()->create([
            'type' => 'admin',
            'email' => '<EMAIL>',
        ]);
        $user->assignRole('user');

        $this->actingAs($this->admin);

        $updatedData = [
            'email' => '<EMAIL>',
            'name' => 'Updated',
            'last_name' => 'Name',
        ];

        Livewire::test(UserResource\Pages\EditUser::class, ['record' => $user->id])
            ->fillForm($updatedData)
            ->call('save')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'email' => '<EMAIL>',
            'name' => 'Updated',
            'last_name' => 'Name',
        ]);
    }

    /** @test */
    public function admin_cannot_edit_other_admins()
    {
        $otherAdmin = User::factory()->create([
            'type' => 'admin',
            'email' => '<EMAIL>',
        ]);
        $otherAdmin->assignRole('admin');

        $this->actingAs($this->admin);

        // Should not be able to access edit page for other admin
        $this->get(UserResource::getUrl('edit', ['record' => $otherAdmin->id]))
            ->assertStatus(403);
    }

    /** @test */
    public function admin_can_delete_user_with_lower_role()
    {
        $user = User::factory()->create([
            'type' => 'admin',
            'email' => '<EMAIL>',
        ]);
        $user->assignRole('user');

        $this->actingAs($this->admin);

        Livewire::test(UserResource\Pages\ListUsers::class)
            ->callTableAction(DeleteAction::class, $user);

        $this->assertModelMissing($user);
    }

    /** @test */
    public function admin_cannot_delete_other_admins()
    {
        $otherAdmin = User::factory()->create([
            'type' => 'admin',
            'email' => '<EMAIL>',
        ]);
        $otherAdmin->assignRole('admin');

        $this->actingAs($this->admin);

        // Should not be able to delete other admin
        $this->expectException(\Exception::class);

        Livewire::test(UserResource\Pages\ListUsers::class)
            ->callTableAction(DeleteAction::class, $otherAdmin);
    }

    /** @test */
    public function super_admin_can_manage_all_users_except_other_super_admins()
    {
        $admin = User::factory()->create(['type' => 'admin']);
        $admin->assignRole('admin');

        $user = User::factory()->create(['type' => 'admin']);
        $user->assignRole('user');

        $this->actingAs($this->superAdmin);

        // Can view all users
        Livewire::test(UserResource\Pages\ListUsers::class)
            ->assertSuccessful()
            ->assertCanSeeTableRecords([$admin, $user]);

        // Can edit admin
        Livewire::test(UserResource\Pages\EditUser::class, ['record' => $admin->id])
            ->assertSuccessful();

        // Can delete admin
        Livewire::test(UserResource\Pages\ListUsers::class)
            ->callTableAction(DeleteAction::class, $admin);

        $this->assertModelMissing($admin);
    }

    /** @test */
    public function user_list_shows_required_information()
    {
        $user = User::factory()->create([
            'type' => 'admin',
            'email' => '<EMAIL>',
            'name' => 'Test',
            'last_name' => 'User',
            'phone_number' => '+218912345678',
        ]);
        $user->assignRole('user');

        $this->actingAs($this->admin);

        Livewire::test(UserResource\Pages\ListUsers::class)
            ->assertSuccessful()
            ->assertSee('<EMAIL>')  // Email
            ->assertSee('Test User')           // Username (name + last_name)
            ->assertSee('+218 91-2345678')     // Phone number (formatted)
            ->assertSee('User');               // Role
    }

    /** @test */
    public function invitation_is_sent_when_creating_new_user()
    {
        $this->actingAs($this->admin);

        $userData = [
            'email' => '<EMAIL>',
            'name' => 'Invited',
            'last_name' => 'User',
            'phone_number' => '+218912345678',
            'gender' => 'male',
            'type' => 'admin',
        ];

        // Mock notification to verify invitation is sent
        \Notification::fake();

        Livewire::test(UserResource\Pages\CreateUser::class)
            ->fillForm($userData)
            ->call('create')
            ->assertHasNoFormErrors();

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);

        // Verify invitation notification was sent
        \Notification::assertSentTo(
            $user,
            \App\Notifications\ResetPasswordNotification::class
        );
    }
}
