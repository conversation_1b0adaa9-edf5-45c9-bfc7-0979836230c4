<?php

namespace Tests\Feature\BackOffice;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\Trips\TripStatus;
use App\Filament\Pages\Dashboard;
use App\Filament\Resources\Panel\ReportResource\Widgets\RevenueAnalyticsApexWidget;
use App\Filament\Resources\Panel\ReportResource\Widgets\TopPerformersWidget;
use App\Filament\Resources\Panel\ReportResource\Widgets\UserGrowthApexWidget;
use App\Filament\Widgets\TripStats;
use App\Filament\Widgets\VehiclesRealTimeMovement;
use App\Models\Driver;
use App\Models\Trip;
use App\Models\User;
use App\Models\Vehicle;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class DashboardTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin role
        Role::create(['name' => 'admin']);

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'type' => 'admin',
        ]);
        $this->admin->assignRole('admin');
    }

    /** @test */
    public function admin_can_access_dashboard()
    {
        $this->actingAs($this->admin);

        Livewire::test(Dashboard::class)
            ->assertSuccessful();
    }

    /** @test */
    public function dashboard_displays_trip_statistics()
    {
        $this->actingAs($this->admin);

        // Create trips with different statuses for today
        Trip::factory()->create([
            'status' => TripStatus::pending,
            'created_at' => Carbon::today(),
        ]);

        Trip::factory()->create([
            'status' => TripStatus::assigned,
            'created_at' => Carbon::today(),
        ]);

        Trip::factory()->create([
            'status' => TripStatus::completed,
            'created_at' => Carbon::today(),
        ]);

        Trip::factory()->create([
            'status' => TripStatus::canceled,
            'created_at' => Carbon::today(),
        ]);

        Trip::factory()->create([
            'status' => TripStatus::on_trip,
            'created_at' => Carbon::today(),
        ]);

        Livewire::test(TripStats::class)
            ->assertSuccessful()
            ->assertSee('Total Trips')
            ->assertSee('5') // Total trips today
            ->assertSee('Accepted Trips')
            ->assertSee('1') // Assigned trips
            ->assertSee('Canceled Trips')
            ->assertSee('1') // Canceled trips
            ->assertSee('Pending Trips')
            ->assertSee('1') // Pending trips
            ->assertSee('Completed Trips')
            ->assertSee('1') // Completed trips
            ->assertSee('Trips In Progress')
            ->assertSee('1'); // On trip
    }

    /** @test */
    public function dashboard_shows_revenue_analytics()
    {
        $this->actingAs($this->admin);

        // Create trips with revenue
        Trip::factory()->count(5)->create([
            'status' => TripStatus::completed,
            'final_price' => 25.50,
            'created_at' => Carbon::today(),
        ]);

        Livewire::test(RevenueAnalyticsApexWidget::class)
            ->assertSuccessful();
    }

    /** @test */
    public function dashboard_shows_user_growth_analytics()
    {
        $this->actingAs($this->admin);

        // Create users over different dates
        User::factory()->count(3)->create([
            'created_at' => Carbon::today()->subDays(7),
        ]);

        User::factory()->count(5)->create([
            'created_at' => Carbon::today()->subDays(3),
        ]);

        User::factory()->count(2)->create([
            'created_at' => Carbon::today(),
        ]);

        Livewire::test(UserGrowthApexWidget::class)
            ->assertSuccessful();
    }

    /** @test */
    public function dashboard_shows_top_performers()
    {
        $this->actingAs($this->admin);

        // Create drivers with different performance metrics
        $driver1 = Driver::factory()->create([
            'global_status' => DriverGlobalStatus::active,
        ]);

        $driver2 = Driver::factory()->create([
            'global_status' => DriverGlobalStatus::active,
        ]);

        // Create trips for drivers
        Trip::factory()->count(10)->create([
            'driver_id' => $driver1->id,
            'status' => TripStatus::completed,
            'final_price' => 30.00,
        ]);

        Trip::factory()->count(5)->create([
            'driver_id' => $driver2->id,
            'status' => TripStatus::completed,
            'final_price' => 25.00,
        ]);

        Livewire::test(TopPerformersWidget::class)
            ->assertSuccessful();
    }

    /** @test */
    public function dashboard_displays_real_time_vehicle_tracking()
    {
        $this->actingAs($this->admin);

        // Create vehicles with different statuses
        $availableVehicle = Vehicle::factory()->create();
        $availableDriver = Driver::factory()->create([
            'global_status' => DriverGlobalStatus::active,
            'location' => 'SRID=4326;POINT(13.1875 32.8872)', // Tripoli coordinates
        ]);
        $availableVehicle->drivers()->attach($availableDriver);

        $onTripVehicle = Vehicle::factory()->create();
        $onTripDriver = Driver::factory()->create([
            'global_status' => DriverGlobalStatus::active,
            'location' => 'SRID=4326;POINT(13.1900 32.8900)',
        ]);
        $onTripVehicle->drivers()->attach($onTripDriver);

        // Create an active trip for the on-trip vehicle
        Trip::factory()->create([
            'driver_id' => $onTripDriver->id,
            'status' => TripStatus::on_trip,
        ]);

        $offlineVehicle = Vehicle::factory()->create();
        $offlineDriver = Driver::factory()->create([
            'global_status' => DriverGlobalStatus::inactive,
            'location' => 'SRID=4326;POINT(13.1850 32.8850)',
        ]);
        $offlineVehicle->drivers()->attach($offlineDriver);

        Livewire::test(VehiclesRealTimeMovement::class)
            ->assertSuccessful();
    }

    /** @test */
    public function dashboard_shows_overall_earnings_statistics()
    {
        $this->actingAs($this->admin);

        // Create completed trips with various prices
        $trips = Trip::factory()->count(10)->create([
            'status' => TripStatus::completed,
            'final_price' => 25.75,
            'created_at' => Carbon::today(),
        ]);

        $totalRevenue = $trips->sum('final_price');
        $averageTrip = $trips->avg('final_price');

        // Test that dashboard shows these statistics
        $response = $this->get('/');
        $response->assertSuccessful();

        // The exact implementation would depend on how the dashboard displays these stats
        // This is a placeholder for the actual dashboard content verification
    }

    /** @test */
    public function dashboard_shows_driver_leaderboard()
    {
        $this->actingAs($this->admin);

        // Create drivers with different ratings and trip counts
        $topDriver = Driver::factory()->create([
            'global_status' => DriverGlobalStatus::active,
        ]);

        $secondDriver = Driver::factory()->create([
            'global_status' => DriverGlobalStatus::active,
        ]);

        $thirdDriver = Driver::factory()->create([
            'global_status' => DriverGlobalStatus::active,
        ]);

        // Create trips for drivers with different completion rates
        Trip::factory()->count(20)->create([
            'driver_id' => $topDriver->id,
            'status' => TripStatus::completed,
        ]);

        Trip::factory()->count(15)->create([
            'driver_id' => $secondDriver->id,
            'status' => TripStatus::completed,
        ]);

        Trip::factory()->count(10)->create([
            'driver_id' => $thirdDriver->id,
            'status' => TripStatus::completed,
        ]);

        Livewire::test(TopPerformersWidget::class)
            ->assertSuccessful();
    }

    /** @test */
    public function dashboard_updates_in_real_time()
    {
        $this->actingAs($this->admin);

        // Test that dashboard widgets are configured for real-time updates
        $tripStatsWidget = Livewire::test(TripStats::class);
        
        // Verify polling is disabled (as per the widget configuration)
        $this->assertNull($tripStatsWidget->instance()->getPollingInterval());
    }

    /** @test */
    public function dashboard_filters_work_correctly()
    {
        $this->actingAs($this->admin);

        // Create trips on different dates
        Trip::factory()->create([
            'status' => TripStatus::completed,
            'created_at' => Carbon::today()->subDays(7),
        ]);

        Trip::factory()->create([
            'status' => TripStatus::completed,
            'created_at' => Carbon::today(),
        ]);

        Livewire::test(Dashboard::class)
            ->assertSuccessful()
            ->set('filters.startDate', Carbon::today()->subDays(1)->format('Y-m-d'))
            ->set('filters.endDate', Carbon::today()->format('Y-m-d'));
    }

    /** @test */
    public function vehicle_tracking_shows_correct_status_colors()
    {
        $this->actingAs($this->admin);

        // This test verifies the business rules for vehicle color coding:
        // Green: Available, Red: On-trip, Gray: Offline

        $widget = Livewire::test(VehiclesRealTimeMovement::class);
        
        // The widget should be successfully rendered
        $widget->assertSuccessful();
        
        // Additional assertions would depend on the specific implementation
        // of the VehiclesRealTimeMovement widget
    }
}
