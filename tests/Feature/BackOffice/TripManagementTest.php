<?php

namespace Tests\Feature\BackOffice;

use App\Enums\Trips\TripStatus;
use App\Filament\Resources\Panel\TripResource;
use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use App\Models\TripLocation;
use App\Models\TripRating;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleType;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class TripManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected Driver $driver;
    protected Rider $rider;
    protected Vehicle $vehicle;
    protected VehicleType $vehicleType;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin role
        Role::create(['name' => 'admin']);

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'type' => 'admin',
        ]);
        $this->admin->assignRole('admin');

        // Create vehicle type
        $this->vehicleType = VehicleType::factory()->create([
            'name' => 'Economy',
        ]);

        // Create driver and rider
        $this->driver = Driver::factory()->create();
        $this->rider = Rider::factory()->create();
        $this->vehicle = Vehicle::factory()->create([
            'vehicle_type_id' => $this->vehicleType->id,
        ]);
    }

    /** @test */
    public function admin_can_view_trips_list()
    {
        $this->actingAs($this->admin);

        // Create multiple trips
        $trips = Trip::factory()->count(5)->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
        ]);

        Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful()
            ->assertCanSeeTableRecords($trips);
    }

    /** @test */
    public function trips_list_shows_required_information()
    {
        $this->actingAs($this->admin);

        // Create trip with specific data
        $trip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::completed,
            'final_price' => 25.50,
            'created_at' => Carbon::now(),
        ]);

        // Create trip locations
        TripLocation::factory()->create([
            'trip_id' => $trip->id,
            'type' => 'pickup',
            'address' => 'Tripoli Central Market',
        ]);

        TripLocation::factory()->create([
            'trip_id' => $trip->id,
            'type' => 'dropoff',
            'address' => 'Tripoli Airport',
        ]);

        Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful()
            ->assertSee($trip->id)                                    // Trip ID
            ->assertSee($trip->created_at->format('Y-m-d'))          // Date
            ->assertSee($trip->created_at->format('H:i'))            // Time
            ->assertSee($this->driver->user->name)                   // Driver name
            ->assertSee($this->rider->user->name)                    // Rider name
            ->assertSee($trip->status->value)                        // Ride Status
            ->assertSee('Tripoli Central Market')                    // Pickup location
            ->assertSee('Tripoli Airport')                          // Drop-off location
            ->assertSee('25.50');                                   // Fare
    }

    /** @test */
    public function trips_are_sorted_by_most_recent_first()
    {
        $this->actingAs($this->admin);

        // Create trips with different timestamps
        $oldTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'created_at' => Carbon::now()->subDays(5),
        ]);

        $recentTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'created_at' => Carbon::now()->subHours(2),
        ]);

        $newestTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'created_at' => Carbon::now(),
        ]);

        $component = Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful();

        // Verify trips are ordered by most recent first
        $tableRecords = $component->instance()->getTableRecords()->toArray();
        $this->assertEquals($newestTrip->id, $tableRecords[0]['id']);
        $this->assertEquals($recentTrip->id, $tableRecords[1]['id']);
        $this->assertEquals($oldTrip->id, $tableRecords[2]['id']);
    }

    /** @test */
    public function admin_can_view_detailed_trip_information()
    {
        $this->actingAs($this->admin);

        // Create trip with comprehensive data
        $trip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::completed,
            'final_price' => 35.75,
            'pickup_time' => Carbon::now()->subHours(2),
            'dropoff_time' => Carbon::now()->subHours(1),
            'rider_notes' => 'Please wait at the main entrance',
            'is_female_only' => true,
        ]);

        // Create trip locations
        TripLocation::factory()->create([
            'trip_id' => $trip->id,
            'type' => 'pickup',
            'address' => 'Tripoli Central Market, Green Square',
        ]);

        TripLocation::factory()->create([
            'trip_id' => $trip->id,
            'type' => 'dropoff',
            'address' => 'Tripoli International Airport, Terminal 1',
        ]);

        // Create trip rating
        TripRating::factory()->create([
            'trip_id' => $trip->id,
            'rating' => 5,
            'trip_review' => 'Excellent service!',
        ]);

        Livewire::test(TripResource\Pages\ViewTrip::class, ['record' => $trip->id])
            ->assertSuccessful()
            ->assertSee($trip->id)                                           // Trip ID
            ->assertSee($trip->created_at->format('Y-m-d'))                 // Date of trip
            ->assertSee($trip->pickup_time->format('H:i'))                  // Time of pickup
            ->assertSee($trip->dropoff_time->format('H:i'))                 // Time of drop-off
            ->assertSee('Tripoli Central Market')                           // Pickup location
            ->assertSee('Tripoli International Airport')                    // Drop-off location
            ->assertSee($this->driver->user->name)                         // Driver first name
            ->assertSee($this->driver->user->last_name)                     // Driver last name
            ->assertSee($this->driver->user->phone_number)                  // Driver phone
            ->assertSee($this->driver->user->gender->value)                 // Driver gender
            ->assertSee($this->rider->user->name)                          // Rider first name
            ->assertSee($this->rider->user->last_name)                      // Rider last name
            ->assertSee($this->rider->user->phone_number)                   // Rider phone
            ->assertSee($this->rider->user->gender->value)                  // Rider gender
            ->assertSee($this->vehicle->license_plate_number)               // License plate
            ->assertSee($this->vehicle->seat_number)                        // Number of seats
            ->assertSee($this->vehicleType->name)                          // Vehicle type
            ->assertSee($this->vehicle->year)                              // Vehicle year
            ->assertSee($this->vehicle->color)                             // Vehicle color
            ->assertSee('35.75')                                           // Total fare
            ->assertSee('Please wait at the main entrance')                // Trip notes
            ->assertSee('Yes')                                             // Women-Only Service
            ->assertSee('5')                                               // Rating
            ->assertSee('Excellent service!');                            // Feedback
    }

    /** @test */
    public function admin_can_view_trip_duration()
    {
        $this->actingAs($this->admin);

        $pickupTime = Carbon::now()->subHours(2);
        $dropoffTime = Carbon::now()->subMinutes(75); // 45 minutes trip

        $trip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'pickup_time' => $pickupTime,
            'dropoff_time' => $dropoffTime,
        ]);

        Livewire::test(TripResource\Pages\ViewTrip::class, ['record' => $trip->id])
            ->assertSuccessful()
            ->assertSee('45'); // Trip duration in minutes
    }

    /** @test */
    public function admin_can_view_vehicle_equipment_information()
    {
        $this->actingAs($this->admin);

        // This would require setting up vehicle equipment relationships
        // For now, we'll test the basic structure
        $trip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
        ]);

        Livewire::test(TripResource\Pages\ViewTrip::class, ['record' => $trip->id])
            ->assertSuccessful();
    }

    /** @test */
    public function admin_can_filter_trips_by_status()
    {
        $this->actingAs($this->admin);

        // Create trips with different statuses
        $completedTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::completed,
        ]);

        $canceledTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::canceled,
        ]);

        $pendingTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::pending,
        ]);

        // Test filtering by completed status
        Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful()
            ->filterTable('status', TripStatus::completed->value)
            ->assertCanSeeTableRecords([$completedTrip])
            ->assertCanNotSeeTableRecords([$canceledTrip, $pendingTrip]);
    }

    /** @test */
    public function admin_can_search_trips()
    {
        $this->actingAs($this->admin);

        $trip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
        ]);

        // Create trip location for search
        TripLocation::factory()->create([
            'trip_id' => $trip->id,
            'type' => 'pickup',
            'address' => 'Unique Search Location',
        ]);

        Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful()
            ->searchTable('Unique Search Location')
            ->assertCanSeeTableRecords([$trip]);
    }

    /** @test */
    public function admin_can_configure_trips_per_page()
    {
        $this->actingAs($this->admin);

        // Create more trips than default page size
        Trip::factory()->count(25)->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
        ]);

        $component = Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful();

        // Test that pagination options are available
        // The exact implementation depends on Filament's table configuration
        $this->assertNotNull($component->instance()->getTable());
    }

    /** @test */
    public function trip_ratings_and_feedback_are_displayed()
    {
        $this->actingAs($this->admin);

        $trip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
        ]);

        // Create multiple ratings for the trip
        TripRating::factory()->create([
            'trip_id' => $trip->id,
            'rating' => 4,
            'trip_review' => 'Good service, but could be faster',
        ]);

        Livewire::test(TripResource\Pages\ViewTrip::class, ['record' => $trip->id])
            ->assertSuccessful()
            ->assertSee('4')
            ->assertSee('Good service, but could be faster');
    }
}
