<?php

namespace Tests\Feature\Api;

use App\Models\Driver;
use App\Models\DriverAvailability;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DriverDriverAvailabilitiesTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Skip API tests as they are not part of the backlog requirements
        $this->markTestSkipped('API routes are not part of the backlog requirements. Focus is on Filament admin panel.');
    }

    /** @test */
    public function it_gets_driver_driver_availabilities()
    {
        $driver = Driver::factory()->create();
        $driverAvailabilities = DriverAvailability::factory()
            ->count(2)
            ->create([
                'driver_id' => $driver->id,
            ]);

        $response = $this->getJson(
            route('api.drivers.driver-availabilities.index', $driver)
        );

        $response->assertOk()->assertSee($driverAvailabilities[0]->notes);
    }

    /** @test */
    public function it_stores_the_driver_driver_availabilities()
    {
        $driver = Driver::factory()->create();
        $data = DriverAvailability::factory()
            ->make([
                'driver_id' => $driver->id,
            ])
            ->toArray();

        $response = $this->postJson(
            route('api.drivers.driver-availabilities.store', $driver),
            $data
        );

        unset($data['created_at']);
        unset($data['updated_at']);

        $this->assertDatabaseHas('driver_availabilities', $data);

        $response->assertStatus(201)->assertJsonFragment($data);

        $driverAvailability = DriverAvailability::latest('id')->first();

        $this->assertEquals($driver->id, $driverAvailability->driver_id);
    }
}
