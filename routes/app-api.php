<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ConfigController;
use App\Http\Controllers\Api\DriverController;
use App\Http\Controllers\Api\DriversDriverAvailabilityController;
use App\Http\Controllers\Api\DriversTripController;
use App\Http\Controllers\Api\DriversVehicleController;
use App\Http\Controllers\Api\RiderController;
use App\Http\Controllers\Api\RidersTripController;
use App\Http\Controllers\Api\RidersTripRatingController;
use App\Http\Controllers\Api\TripController;
use App\Http\Controllers\Api\TripPriceController;
use App\Http\Controllers\Api\TripRatingController;
use App\Http\Controllers\Api\TripsTripRatingController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\UsersDriverController;
use App\Http\Controllers\Api\UsersRiderController;
use App\Http\Controllers\Api\VehicleController;
use App\Http\Controllers\Api\VehiclesDriverController;
use App\Http\Controllers\Api\VehiclesTripController;
use Illuminate\Support\Facades\Route;

Route::name('api.')
    ->prefix('api')
    ->group(function () {

        Route::post('/login', [AuthController::class, 'login'])->name(
            'login'
        );

        Route::post('/otp', [AuthController::class, 'otp'])->name(
            'api.otp'
        );

        Route::get('/config', [ConfigController::class, 'configuration']);

        Route::post('refresh-token', [AuthController::class, 'refresh'])
            ->name('api.refresh');

        Route::middleware('auth:sanctum')->group(function () {

            Route::post('users/logout', [AuthController::class, 'logout'])
                ->name('api.logout');

            Route::get('/me', [UserController::class, 'me'])->name('api.me');

            Route::post('users/verify email', [AuthController::class, 'verify_email'])->name(
                'user.verify email');

            Route::patch('users/fcm-token', [AuthController::class, 'register_fcm'])
                ->name('fcm-token');

            // Route::get('/trips/{trip}/trip-ratings', [
            //     TripsTripRatingController::class,
            //     'index',
            // ])->name('trips.trip-ratings.index');

            // Route::post('/trips/{trip}/trip-ratings', [
            //     TripsTripRatingController::class,
            //     'store',
            // ])->name('trips.trip-ratings.store');

            // Route::get('/trips', [TripController::class, 'index'])->name(
            //     'trips.index'
            // );

            Route::post('/trips', [TripController::class, 'store'])->name(
                'trips.store'
            );

            Route::patch('/trips/{id}', [TripController::class, 'update'])
                ->name('trips.request.update');

            Route::patch('/trips/edit/{trip_id}', [RidersTripController::class, 'editTripRequest'])
                ->name('trips.active.edit');

            Route::get('/trip/{id}', [TripController::class, 'Tripstatus'])->name('trips.status');

            Route::post('trips/cancel-driver-search/{id}', [TripController::class, 'cancelDriverSearch']);
            Route::get('/trips/{trip}', [TripController::class, 'show'])->name(
                'trips.show'
            );

            // Route::put('/trips/{trip}', [
            //     TripController::class,
            //     'update',
            // ])->name('trips.update');

            // ( to be deleted )
            // Route::post('rider/trip/add-favorite', [
            //     RidersTripController::class,
            //     'addToFavorite',
            // ])->name('trip.add-favorite');

            Route::post('rider/address/set-shortcut/{addressId}', [
                RiderController::class,
                'setAddressShortcut',
            ])->name('address.set-shortcut');

            Route::post('/trips/confirm/{id}', [TripController::class, 'confirmTrip'])
                ->name('vehicle.types');

            // to be removed
            // Route::post('/trips/{id}/update-arrival', [TripPriceController::class, 'updateTripArrival'])
            //     ->name('trips.update-arrival');

            // Route::post('/vehicles/{vehicle}/trips', [
            //     VehiclesTripController::class,
            //     'store',
            // ])->name('vehicles.trips.store');

            // Route::get('/vehicles/{vehicle}/drivers', [
            //     VehiclesDriverController::class,
            //     'index',
            // ])->name('vehicles.drivers.index');

            // Route::post('/vehicles/{vehicle}/drivers/{driver}', [
            //     VehiclesDriverController::class,
            //     'store',
            // ])->name('vehicles.drivers.store');

            // Route::delete('/vehicles/{vehicle}/drivers/{driver}', [
            //     VehiclesDriverController::class,
            //     'destroy',
            // ])->name('vehicles.drivers.destroy');

            // Route::get('/vehicles', [VehicleController::class, 'index'])->name(
            //     'vehicles.index'
            // );

            // Route::post('/vehicles', [VehicleController::class, 'store'])->name(
            //     'vehicles.store'
            // );

            // Route::get('/vehicles/{vehicle}', [
            //     VehicleController::class,
            //     'show',
            // ])->name('vehicles.show');

            // Route::put('/vehicles/{vehicle}', [
            //     VehicleController::class,
            //     'update',
            // ])->name('vehicles.update');

            // Route::delete('/vehicles/{vehicle}', [
            //     VehicleController::class,
            //     'destroy',
            // ])->name('vehicles.destroy');

            Route::get('rider/home', [RiderController::class, 'home'])
                ->name('rider.home');

            Route::get('rider/share-location/{tripId}', [UsersRiderController::class, 'shareLocation'])
                ->name('rider.share-location');

            Route::get('rider/my-trips', [
                RidersTripController::class,
                'tripList',
            ])->name('rider.trips.list');

            Route::get('rider/favorite/addresses', [RidersTripController::class, 'favoriteAddresses'])
                ->name('rider.favorite.addresses');

            Route::get('rider/favorite/trips', [RidersTripController::class, 'favoriteTrips'])
                ->name('rider.favorite.trips');

            // Route::post('/riders/{rider}/trips', [
            //     RidersTripController::class,
            //     'store',
            // ])->name('riders.trips.store');

            Route::get('rider/trip/{trip}', [
                RidersTripController::class,
                'trip',
            ])->name('rider.trip.details');

            // Route::get('/riders/{rider}/trip-ratings', [
            //     RidersTripRatingController::class,
            //     'index',
            // ])->name('riders.trip-ratings.index');

            // Route::post('/riders/{rider}/trip-ratings', [
            //     RidersTripRatingController::class,
            //     'store',
            // ])->name('riders.trip-ratings.store');

            // Route::get('/riders', [RiderController::class, 'index'])->name(
            //     'riders.index'
            // );

            // Route::post('/riders', [RiderController::class, 'store'])->name(
            //     'riders.store'
            // );

            // Route::get('/riders/{rider}', [
            //     RiderController::class,
            //     'show',
            // ])->name('riders.show');

            // Route::put('/riders/{rider}', [
            //     RiderController::class,
            //     'update',
            // ])->name('riders.update');

            Route::post('rider/preferences', [RiderController::class, 'preferences'])
                ->name('rider.preferences');

            // Route::delete('/riders/{rider}', [
            //     RiderController::class,
            //     'destroy',
            // ])->name('riders.destroy');

            Route::get('driver/my-trips', [
                DriversTripController::class,
                'tripList',
            ])->name('drivers.trips.list');

            Route::get('driver/trip/{tripId}', [
                DriversTripController::class,
                'trip',
            ])->name('driver.trip.details');

            Route::post('driver/trip-ratings', [
                DriversTripController::class,
                'driverReview',
            ])->name('driver.trip-ratings');

            // Route::get('/drivers/{driver}/driver-availabilities', [
            //     DriversDriverAvailabilityController::class,
            //     'index',
            // ])->name('drivers.driver-availabilities.index');

            // Route::post('/drivers/{driver}/driver-availabilities', [
            //     DriversDriverAvailabilityController::class,
            //     'store',
            // ])->name('drivers.driver-availabilities.store');

            // Route::get('/drivers/{driver}/vehicles', [
            //     DriversVehicleController::class,
            //     'index',
            // ])->name('drivers.vehicles.index');

            // Route::post('/drivers/{driver}/vehicles/{vehicle}', [
            //     DriversVehicleController::class,
            //     'store',
            // ])->name('drivers.vehicles.store');

            // Route::delete('/drivers/{driver}/vehicles/{vehicle}', [
            //     DriversVehicleController::class,
            //     'destroy',
            // ])->name('drivers.vehicles.destroy');
            Route::get('/driver/profile', [DriverController::class, 'profile']);

            Route::patch('/driver/location', [DriverController::class, 'updateLocationHttp'])
                ->name('driver.location.update');

            Route::post('driver/documents', [DriverController::class, 'uploadDocuments'])
                ->name('driver.documents');

            // Route::get('/drivers', [DriverController::class, 'index'])->name(
            //     'drivers.index'
            // );

            Route::get('driver/vehicles/list', [DriversVehicleController::class, 'ListVehicles'])
                ->name('drivers.vehicles.list');

            Route::get('driver/vehicle/{vehicleId}', [DriversVehicleController::class, 'vehicleDetails'])
                ->name('drivers.vehicles.detials');

            Route::post('/driver/vehicles', [DriverController::class, 'addVehicle'])
                ->name('drivers.vehicle.create');

            Route::delete('/driver/vehicle/{vehicleId}', [DriversVehicleController::class, 'DeleteVehicle'])
                ->name('drivers.vehicle.delete');

            // need to be patch and naming need to be changed
            Route::post('/driver/vehicle/{vehicle?}', [DriverController::class, 'UpdateVehicle'])
                ->name('drivers.vehicle.update');

            // Route::get('/drivers/{driver}', [
            //     DriverController::class,
            //     'show',
            // ])->name('drivers.show');

            // Route::put('/drivers/{driver}', [
            //     DriverController::class,
            //     'update',
            // ])->name('drivers.update');

            // Route::delete('/drivers/{driver}', [
            //     DriverController::class,
            //     'destroy',
            // ])->name('drivers.destroy');

            // Route::get('/users/{user}/riders', [
            //     UsersRiderController::class,
            //     'index',
            // ])->name('users.riders.index');

            // Route::post('/users/{user}/riders', [
            //     UsersRiderController::class,
            //     'store',
            // ])->name('users.riders.store');

            // Route::get('/users/{user}/drivers', [
            //     UsersDriverController::class,
            //     'index',
            // ])->name('users.drivers.index');

            // Route::post('/users/{user}/drivers', [
            //     UsersDriverController::class,
            //     'store',
            // ])->name('users.drivers.store');

            // Route::get('/users', [UserController::class, 'index'])->name(
            //     'users.index'
            // );

            // Route::post('/users', [UserController::class, 'store'])->name(
            //     'users.store'
            // );

            // add a new address with it's label
            Route::post('rider/favorite-addresses', [RiderController::class, 'addFavoriteAddress'])
                ->name('rider.favorite-addresses.add');

            Route::post('rider/addresses/label', [RiderController::class, 'updateLabel'])
                ->name('rider.favorite-addresses.update');

            Route::delete('rider/addresses/label/{addressId}', [RiderController::class, 'removeLabel'])
                ->name('rider.favorite-addresses.label.remove');
            // just save an address
            Route::post('rider/trip/favorite-addresses', [RidersTripController::class, 'saveFavoriteAddress'])
                ->name('rider.favorite-addresses.save');

            Route::post('upload-picture', [UserController::class, 'picture'])->name(
                'user.picture'
            );

            Route::post('user/status', [UserController::class, 'status'])->name(
                'user.update.status'
            );

            Route::post('rider/trip-ratings', [TripRatingController::class, 'riderReview'])->name('rider.trip-ratings');

            Route::post('/rider/heartbeat', [RiderController::class, 'heartbeat'])
                ->name('rider.heartbeat');
            // Route::get('/users/{user}', [UserController::class, 'show'])->name(
            //     'users.show'
            // );

            // Route::put('/users/{user}', [
            //     UserController::class,
            //     'update',
            // ])->name('users.update');

            Route::patch('users/profile', [UserController::class, 'patch'])->name(
                'user.profile');

            // Route::delete('/users/{user}', [
            //     UserController::class,
            //     'destroy',
            // ])->name('users.destroy');

            // Route::post('/trip-ratings', [
            //     TripRatingController::class,
            //     'store',
            // ])->name('trip-ratings.store');

            // Route::get('/trip-ratings/{tripRating}', [
            //     TripRatingController::class,
            //     'show',
            // ])->name('trip-ratings.show');

            // Route::put('/trip-ratings/{tripRating}', [
            //     TripRatingController::class,
            //     'update',
            // ])->name('trip-ratings.update');

            // Route::delete('/trip-ratings/{tripRating}', [
            //     TripRatingController::class,
            //     'destroy',
            // ])->name('trip-ratings.destroy');

            // Route::post('/destination', [RidersTripController::class, 'EditTripRequest'])->name(
            //     'getAvailableDrivers'
            // );

        });
    });
