<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class TripCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request)
    {
        $data = [];

        if ($this->collection->isNotEmpty() && $this->collection[0]->status->value == 'completed') {
            $data = $this->collection->map(function ($item) {
                return [
                    'id' => $item->id,
                    'status' => $item->status->value ?? null,
                    'distance' => $item->distance ?? null,
                    'polyline' => $item->tripLocation->polyline ?? null,
                    'notes' => $item->rider_notes ?? null,
                    'estimated_departure_time' => $item->estimated_departure_time ? $item->estimated_departure_time->toIso8601String() : null,
                    'actual_departure_time' => $item->actual_departure_time ? $item->actual_departure_time->toIso8601String() : null,
                    'estimated_arrival_time' => $item->estimated_arrival_time ? $item->estimated_arrival_time->toIso8601String() : null,
                    'actual_arrival_time' => $item->actual_arrival_time ? $item->actual_arrival_time->toIso8601String() : null,
                    'date' => $item->created_at->format('Y-m-d'),
                    'total_price' => $item->total_price ?? null,
                    'duration' => $item->tripDuration() ? $item->tripDuration()->format('%i') : null,
                    'pricing_breakdown' => json_decode($item->pricing_breakdown, true),
                    'departureAddress' => [
                        'departure_address' => $item->tripLocation?->departure_address ?? null,
                        'address_label' => $item->departure_label ?? null,
                        'latitude' => $item->tripLocation?->departure_lat ?? null,
                        'longitude' => $item->tripLocation?->departure_lng ?? null,
                    ],
                    'arrivalAddress' => [
                        'arrival_address' => $item->tripLocation?->arrival_address ?? null,
                        'address_label' => $item->arrival_label ?? null,
                        'latitude' => $item->tripLocation?->arrival_lat ?? null,
                        'longitude' => $item->tripLocation?->arrival_lng ?? null,
                    ],
                    'driver' => [
                        'id' => $item->driver?->id ?? null,
                        'name' => $item->driver?->user?->name ?? null,
                        'gender' => $item->driver?->user?->gender ?? null,
                        'driver_image' => $item->driver?->user?->cover_picture
                            ? env('APP_URL', '/').'/storage/'.$item->driver->user->cover_picture
                            : env('APP_URL', '/').'/images/avatar.png',
                        'driver_rating' => $item->rating ?? null,
                    ],
                    'rider' => [
                        'id' => $item->rider?->id ?? null,
                        'name' => $item->rider?->user?->name ?? null,
                        'last_name' => $item->rider?->user?->last_name ?? null,
                        'full_name' => $item->rider?->user?->full_name ?? null,
                        'gender' => $item->rider?->user?->gender ?? null,
                        'rider_image' => $item->rider?->user?->cover_picture
                            ? env('APP_URL', '/').'/storage/'.$item->rider->user->cover_picture
                            : env('APP_URL', '/').'/images/avatar.png',
                    ],
                    'vehicle' => [
                        'id' => $item->vehicle?->id ?? null,
                        'vehicle_brand' => $item->vehicle?->vehicleModel?->vehicleBrand?->id ?? null,
                        'vehicle_model' => $item->vehicle?->vehicleModel?->name_en ?? null,
                        'vehicle_type' => $item->vehicle?->vehicleType?->name_en ?? null,
                        'license_plate_number' => $item->vehicle?->license_plate_number ?? null,
                        'vehicle_image' => $item->vehicle?->image
                            ? env('APP_URL', '/').'/storage/'.$item->vehicle?->image
                            : env('APP_URL', '/').'/images/vehicle.jpg',
                        'vehicle_rating' => $item->tripRatings()->exists() ? $item->tripRatings->first()->rider_to_car_rating : null,
                    ],
                ];
            });
        } else {
            $data = $this->collection->map(function ($item) {
                return [
                    'id' => $item->id,
                    'status' => $item->status->value ?? null,
                    'distance' => $item->distance ?? null,
                    'polyline' => $item->tripLocation->polyline ?? null,
                    'notes' => $item->rider_notes ?? null,
                    'estimated_departure_time' => $item->estimated_departure_time ? $item->estimated_departure_time->toIso8601String() : null,
                    'actual_departure_time' => $item->actual_departure_time ? $item->actual_departure_time->toIso8601String() : null,
                    'estimated_arrival_time' => $item->estimated_arrival_time ? $item->estimated_arrival_time->toIso8601String() : null,
                    'actual_arrival_time' => $item->actual_arrival_time ? $item->actual_arrival_time->toIso8601String() : null,
                    'date' => $item->created_at->format('Y-m-d'),
                    'total_price' => $item->total_price ?? null,
                    'duration' => $item->tripDuration() ? $item->tripDuration()->format('%i') : null,
                    'pricing_breakdown' => json_decode($item->pricing_breakdown, true),
                    'departureAddress' => [
                        'departure_address' => $item->tripLocation?->departure_address ?? null,
                        'address_label' => $item->departure_label ?? null,
                        'latitude' => $item->tripLocation?->departure_lat ?? null,
                        'longitude' => $item->tripLocation?->departure_lng ?? null,
                    ],
                    'arrivalAddress' => [
                        'arrival_address' => $item->tripLocation?->arrival_address ?? null,
                        'address_label' => $item->arrival_label ?? null,
                        'latitude' => $item->tripLocation?->arrival_lat ?? null,
                        'longitude' => $item->tripLocation?->arrival_lng ?? null,
                    ],
                    'driver' => [
                        'id' => $item->driver?->id ?? null,
                        'name' => $item->driver?->user?->name ?? null,
                        'gender' => $item->driver?->user?->gender ?? null,
                        'driver_image' => $item->driver?->user?->cover_picture
                            ? env('APP_URL', '/').'/storage/'.$item->driver->user->cover_picture
                            : env('APP_URL', '/').'/images/avatar.png',
                        'driver_rating' => $item->rating ?? null,
                    ],
                    'rider' => [
                        'id' => $item->rider?->id ?? null,
                        'name' => $item->rider?->user?->name ?? null,
                        'last_name' => $item->rider?->user?->last_name ?? null,
                        'full_name' => $item->rider?->user?->full_name ?? null,
                        'gender' => $item->rider?->user?->gender ?? null,
                        'rider_image' => $item->rider?->user?->cover_picture
                            ? env('APP_URL', '/').'/storage/'.$item->rider->user->cover_picture
                            : env('APP_URL', '/').'/images/avatar.png',
                    ],
                    'vehicle' => [
                        'id' => $item->vehicle?->id ?? null,
                        'vehicle_brand' => $item->vehicle?->vehicleModel?->vehicleBrand?->id ?? null,
                        'vehicle_model' => $item->vehicle?->vehicleModel?->name_en ?? null,
                        'vehicle_type' => $item->vehicle?->vehicleType?->name_en ?? null,
                        'license_plate_number' => $item->vehicle?->license_plate_number ?? null,
                        'vehicle_image' => $item->vehicle?->image
                            ? env('APP_URL', '/').'/storage/'.$item->vehicle?->image
                            : env('APP_URL', '/').'/images/vehicle.jpg',
                        'vehicle_rating' => $item->tripRatings()->exists() ? $item->tripRatings->first()->rider_to_car_rating : null,
                    ],
                ];
            });
        }

        return [
            'rides' => $data,
            'meta' => [
                'current_page' => $this->currentPage(),
                'from' => $this->firstItem(),
                'to' => $this->lastItem(),
                'per_page' => $this->perPage(),
                'total' => $this->total(),
                'last_page' => $this->lastPage(),
                'next_page_url' => $this->nextPageUrl(),
                'prev_page_url' => $this->previousPageUrl(),
            ],
        ];
    }
}
