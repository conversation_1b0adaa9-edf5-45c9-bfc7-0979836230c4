<?php

namespace App\Livewire\PricingRules;

use App\Models\PricingRules;
use App\Rules\CombinedPricingRuleConflictRule;
use App\Rules\PricingRuleConflictRule;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Actions\Action as NotificationAction;
use Filament\Notifications\Notification;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\On;
use Livewire\Component;

class GlobalPricingRules extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public PricingRules $record;

    // Properties to store detailed error messages
    public ?string $baseFareDetailedError = null;

    public ?string $distanceFareDetailedError = null;

    public ?string $combinedDetailedError = null;

    // Property to track if we have field validation errors
    public bool $hasFieldValidationErrors = false;

    // Modal properties
    public bool $showErrorModal = false;

    public string $modalTitle = '';

    public string $modalContent = '';

    public function mount(): void
    {
        // Get or create the pricing rules record
        $this->record = PricingRules::firstOrCreate(
            ['id' => 1],
            [
                'global_base_price' => 5.00,
                'global_price_per_km' => 5.00,
                'time_threshold_percentage' => 0.00,
            ]
        );

        $this->form->fill($this->record->attributesToArray());
    }

    public function form(Form $form): Form
    {
        // Get current values for validation
        $currentGlobalBasePrice = $this->record->global_base_price;
        $currentGlobalPricePerKm = $this->record->global_price_per_km;

        return $form
            ->schema([
                Section::make('Global Rules')
                    ->description('Configure the foundational pricing rules that are universally applied to all rides. These rules determine the base fare, distance-based rates, and time threshold for acceptable deviations before penalties are enforced. Adjust these settings to reflect your global pricing strategy effectively.')
                    ->schema([
                        TextInput::make('global_base_price')
                            ->label('Base Fare (B)')
                            ->helperText('A fixed starting fee applied to all rides. Must be greater than 0.01 and at most 100.')
                            ->suffix('LYD')
                            ->extraInputAttributes([
                                'inputmode' => 'decimal',
                                'pattern' => '[0-9]*([.][0-9]{1,6})?', // Allows up to 6 decimal places
                                'maxlength' => 6, // Limit the number of characters
                                'oninput' => "this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');", // Allow only numbers and one decimal point
                            ])
                            ->rules([
                                'required',
                                'numeric',
                                'gt:0.01',
                                'lte:100',
                                function () use ($currentGlobalBasePrice, $currentGlobalPricePerKm) {
                                    return function (string $attribute, $value, \Closure $fail) use ($currentGlobalBasePrice, $currentGlobalPricePerKm) {
                                        // Only validate if the value is being decreased
                                        if ((float) $value < (float) $currentGlobalBasePrice) {
                                            // For base pricing, we need to pass the current distance price as the other value
                                            $rule = new PricingRuleConflictRule('base', (float) $currentGlobalPricePerKm);
                                            if (! $rule->passes($attribute, $value)) {
                                                // Store the detailed message for later use in modal
                                                $this->baseFareDetailedError = $rule->message();
                                                $this->hasFieldValidationErrors = true;

                                                // Show a simpler message under the field
                                                $fail('Lowering the base fare will affect pricing in other components.');
                                            }
                                        }
                                    };
                                },
                            ])
                            ->validationMessages([
                                'gt' => 'The base fare must be greater than 0.01.',
                                'lte' => 'The base fare must not exceed 100.',
                            ]),

                        TextInput::make('global_price_per_km')
                            ->label('Distance-Based Pricing (D)')
                            ->helperText('Rate applied per unit of distance traveled. Must be greater than 0.01 and at most 100.')
                            ->suffix('LYD')
                            ->extraInputAttributes([
                                'inputmode' => 'decimal',
                                'pattern' => '[0-9]*([.][0-9]{1,6})?', // Allows up to 6 decimal places
                                'maxlength' => 6,
                                'oninput' => "this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');",
                            ])
                            ->rules([
                                'required',
                                'numeric',
                                'gt:0.01',
                                'lte:100',
                                function () use ($currentGlobalBasePrice, $currentGlobalPricePerKm) {
                                    return function (string $attribute, $value, \Closure $fail) use ($currentGlobalBasePrice, $currentGlobalPricePerKm) {
                                        // Only validate if the value is being decreased
                                        if ((float) $value < (float) $currentGlobalPricePerKm) {
                                            // For distance pricing, we need to pass the current base price as the other value
                                            $rule = new PricingRuleConflictRule('distance', (float) $currentGlobalBasePrice);
                                            if (! $rule->passes($attribute, $value)) {
                                                // Store the detailed message for later use in modal
                                                $this->distanceFareDetailedError = $rule->message();
                                                $this->hasFieldValidationErrors = true;

                                                // Show a simpler message under the field
                                                $fail('Lowering the distance fare will affect pricing in other components.');
                                            }
                                        }
                                    };
                                },
                            ])
                            ->validationMessages([
                                'gt' => 'The distance-based price must be greater than 0.01.',
                                'lte' => 'The distance-based price must not exceed 100.',
                            ]),
                        TextInput::make('time_threshold_percentage')
                            ->label('Time Threshold (T)')
                            ->helperText('Acceptable deviation percentage for ride duration before penalties apply. Example: 20%')
                            ->suffix('%')
                            ->extraInputAttributes([
                                'inputmode' => 'decimal',
                                'pattern' => '[0-9.]*',
                                'maxlength' => 6,
                                'oninput' => "this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');",
                            ])
                            ->rules(['required', 'numeric', 'gte:0', 'lte:100'])
                            ->validationMessages([
                                'gte' => 'The time threshold must be at least 0%.',
                                'lte' => 'The time threshold must not exceed 100%.',
                            ]),
                    ])->columns(3),
            ])
            ->statePath('data')
            ->model($this->record);
    }

    /**
     * Shows the error details modal
     *
     * @param  string  $type  The type of error to show ('base', 'distance', or 'combined')
     */
    #[On('show-error-modal')]
    public function showErrorDetailsModal($type = null): void
    {
        if (is_array($type) && isset($type['type'])) {
            $type = $type['type'];
        }

        $this->modalTitle = match ($type) {
            'base' => 'Base Fare Pricing Conflicts',
            'distance' => 'Distance Fare Pricing Conflicts',
            'combined' => 'Pricing Rule Conflicts',
            'tabbed' => 'Pricing Rule Conflicts',
            default => 'Pricing Conflicts',
        };

        $this->modalContent = match ($type) {
            'base' => $this->baseFareDetailedError ?? 'No details available',
            'distance' => $this->distanceFareDetailedError ?? 'No details available',
            'combined' => $this->combinedDetailedError ?? 'No details available',
            'tabbed' => $this->buildTabbedContent(),
            default => 'No error details available.',
        };

        $this->showErrorModal = true;
    }

    /**
     * Close the error details modal
     */
    public function closeErrorModal(): void
    {
        $this->showErrorModal = false;
    }

    /**
     * Show a single notification for field validation errors
     */
    private function showFieldValidationNotification(): void
    {
        $hasBaseError = ! empty($this->baseFareDetailedError);
        $hasDistanceError = ! empty($this->distanceFareDetailedError);

        if (! $hasBaseError && ! $hasDistanceError) {
            return;
        }

        $title = 'Pricing Rule Conflicts';
        $body = 'Lowering the pricing will affect other components. Please review the details.';

        if ($hasBaseError && $hasDistanceError) {
            $body = 'Lowering both base and distance pricing will affect other components. Please review the details.';
        } elseif ($hasBaseError) {
            $body = 'Lowering the base fare will affect other components. Please review the details.';
        } elseif ($hasDistanceError) {
            $body = 'Lowering the distance fare will affect other components. Please review the details.';
        }

        Notification::make()
            ->warning()
            ->title($title)
            ->body($body)
            ->persistent()
            ->actions([
                NotificationAction::make('view-details')
                    ->button()
                    ->label('View Details')
                    ->color('warning')
                    ->close()
                    ->dispatch('show-error-modal', [
                        'type' => 'tabbed',
                    ]),
            ])
            ->send();
    }

    /**
     * Build tabbed content for the modal
     */
    private function buildTabbedContent(): string
    {
        $hasBaseError = ! empty($this->baseFareDetailedError);
        $hasDistanceError = ! empty($this->distanceFareDetailedError);

        if (! $hasBaseError && ! $hasDistanceError) {
            return 'No conflicts found.';
        }

        $content = "<div class='space-y-4'>";

        // Tab navigation
        $content .= "<div class='border-b border-gray-200 dark:border-gray-700'>";
        $content .= "<nav class='-mb-px flex space-x-8'>";

        if ($hasBaseError) {
            $content .= "<button type='button' onclick='showTab(\"base-tab\")' id='base-tab-btn' class='tab-button cursor-pointer active border-b-2 border-warning-500 py-2 px-1 text-sm font-medium text-warning-600 focus:outline-none'>Base Fare Conflicts</button>";
        }

        if ($hasDistanceError) {
            $activeClass = ! $hasBaseError ? 'active border-b-2 border-warning-500 text-warning-600' : 'border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300';
            $content .= "<button type='button' onclick='showTab(\"distance-tab\")' id='distance-tab-btn' class='tab-button cursor-pointer {$activeClass} py-2 px-1 text-sm font-medium focus:outline-none'>Distance Fare Conflicts</button>";
        }

        $content .= '</nav>';
        $content .= '</div>';

        // Tab content
        if ($hasBaseError) {
            $display = 'block';
            $content .= "<div id='base-tab' class='tab-content' style='display: {$display};'>";
            $content .= "<div class='py-4'>";
            $content .= $this->baseFareDetailedError;
            $content .= '</div>';
            $content .= '</div>';
        }

        if ($hasDistanceError) {
            $display = ! $hasBaseError ? 'block' : 'none';
            $content .= "<div id='distance-tab' class='tab-content' style='display: {$display};'>";
            $content .= "<div class='py-4'>";
            $content .= $this->distanceFareDetailedError;
            $content .= '</div>';
            $content .= '</div>';
        }

        $content .= '</div>';

        return $content;
    }

    /**
     * Generate test content for modal scrolling
     */
    private function generateTestContent(): string
    {
        return "<div class='space-y-6'>
            <h4 class='font-medium text-warning-600 mb-3'>Test Scrollbar Content:</h4>
            <p class='text-sm text-gray-600 dark:text-gray-400 mb-3'>This is test content to verify the scrollbar is working properly.</p>

            <div class='mb-4'>
                <h5 class='font-medium text-gray-800 dark:text-gray-200 mb-2'>Areas</h5>
                <ul class='list-disc list-inside space-y-1 ml-4'>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>Test Area 1 — Current: 5.00 LYD, Minimum allowed: 2.50 LYD</li>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>Test Area 2 — Current: 3.00 LYD, Minimum allowed: 2.50 LYD</li>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>Test Area 3 — Current: 4.00 LYD, Minimum allowed: 2.50 LYD</li>
                </ul>
            </div>

            <div class='mb-4'>
                <h5 class='font-medium text-gray-800 dark:text-gray-200 mb-2'>Vehicle Types</h5>
                <ul class='list-disc list-inside space-y-1 ml-4'>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>Economy Vehicle — Current: 2.00 LYD, Minimum allowed: 2.50 LYD</li>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>Comfort Vehicle — Current: 1.50 LYD, Minimum allowed: 2.50 LYD</li>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>Luxury Vehicle — Current: 1.00 LYD, Minimum allowed: 2.50 LYD</li>
                </ul>
            </div>

            <div class='mb-4'>
                <h5 class='font-medium text-gray-800 dark:text-gray-200 mb-2'>Seat Configurations</h5>
                <ul class='list-disc list-inside space-y-1 ml-4'>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>4 Seats — Current: 1.50 LYD, Minimum allowed: 2.50 LYD</li>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>6 Seats — Current: 2.00 LYD, Minimum allowed: 2.50 LYD</li>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>8 Seats — Current: 1.75 LYD, Minimum allowed: 2.50 LYD</li>
                </ul>
            </div>

            <div class='mb-4'>
                <h5 class='font-medium text-gray-800 dark:text-gray-200 mb-2'>Gender Rules</h5>
                <ul class='list-disc list-inside space-y-1 ml-4'>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>Female Gender Rule — Current: 1.00 LYD, Minimum allowed: 2.50 LYD</li>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>Male Gender Rule — Current: 1.25 LYD, Minimum allowed: 2.50 LYD</li>
                </ul>
            </div>

            <div class='mb-4'>
                <h5 class='font-medium text-gray-800 dark:text-gray-200 mb-2'>Day-Time Configurations</h5>
                <ul class='list-disc list-inside space-y-1 ml-4'>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>Peak Hour (17:00 - 19:00) — Current: 1.50 LYD, Minimum allowed: 2.50 LYD</li>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>Peak Hour (07:00 - 09:00) — Current: 1.75 LYD, Minimum allowed: 2.50 LYD</li>
                    <li class='text-sm text-gray-700 dark:text-gray-300'>Peak Hour (12:00 - 14:00) — Current: 1.25 LYD, Minimum allowed: 2.50 LYD</li>
                </ul>
            </div>

            <div class='mt-4 pt-3 border-t border-gray-200 dark:border-gray-700'>
                <p class='flex items-start'>
                    <span>Please update the pricing for the components mentioned above before reducing the global pricing.</span>
                </p>
            </div>
        </div>";
    }

    /**
     * Saves the global pricing rules to the database.
     *
     * This method first validates for combined conflicts, then formats the numbers
     * before saving them and updates the pricing rules record with the new values.
     */
    public function save(): void
    {
        // Reset field validation errors flag
        $this->hasFieldValidationErrors = false;

        // Validate the form first - this will trigger individual field validation
        try {
            $data = $this->form->getState();
        } catch (\Illuminate\Validation\ValidationException $e) {
            // If there are field validation errors, show a single notification with tabs
            if ($this->hasFieldValidationErrors) {
                $this->showFieldValidationNotification();
            }
            throw $e; // Re-throw to show field-level errors
        }

        // Format numbers before saving - convert to float first to ensure proper handling
        $newBasePrice = (float) $data['global_base_price'];
        $newDistancePrice = (float) $data['global_price_per_km'];
        $data['time_threshold_percentage'] = (float) $data['time_threshold_percentage'];

        // Check if both base and distance prices are being decreased simultaneously
        $currentBasePrice = (float) $this->record->global_base_price;
        $currentDistancePrice = (float) $this->record->global_price_per_km;

        $bothDecreasing = ($newBasePrice < $currentBasePrice) && ($newDistancePrice < $currentDistancePrice);

        // If both are decreasing, show combined conflicts modal (in addition to individual field validation)
        if ($bothDecreasing) {
            $combinedRule = new CombinedPricingRuleConflictRule(
                $newBasePrice,
                $newDistancePrice,
                $currentBasePrice,
                $currentDistancePrice
            );

            if (! $combinedRule->passes('combined', [$newBasePrice, $newDistancePrice])) {
                // Store the detailed message for later use in modal
                $this->combinedDetailedError = $combinedRule->message();

                // Show detailed error in notification with modal action
                Notification::make()
                    ->warning()
                    ->title('Combined Pricing Rule Conflicts')
                    ->body('Lowering both base and distance pricing will affect other components. Please review the combined impact.')
                    ->persistent()
                    ->actions([
                        NotificationAction::make('view-details')
                            ->button()
                            ->label('View Combined Details')
                            ->color('warning')
                            ->close()
                            ->dispatch('show-error-modal', [
                                'type' => 'combined',
                            ]),
                    ])
                    ->send();

                return; // Don't save if there are conflicts
            }
        }

        // Update the data array with formatted values
        $data['global_base_price'] = $newBasePrice;
        $data['global_price_per_km'] = $newDistancePrice;

        $this->record->update($data);

        // Refresh form with updated values
        $this->form->fill($this->record->fresh()->attributesToArray());

        // Emit event to notify other components
        $this->dispatch('pricing-rules-updated');

        Notification::make()
            ->title('Update Successful')
            ->body('Global rules updated successfully.')
            ->success()
            ->send();
    }

    public function render(): View
    {
        return view('livewire.pricing-rules.global-pricing-rules');
    }
}
