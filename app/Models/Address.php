<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Address extends Model
{
    use HasFactory;

    protected $fillable = [
        'id',
        'addressable_type',
        'addressable_id',
        'address',
        'full_address',
        'postal_address',
        'latitude',
        'longitude',
        'address_label_id',
        'is_favorite',
        'shortcut',
    ];

    /**
     * Cast JSON fields for easier manipulation.
     */
    protected $casts = [
        'postal_address' => 'array',
        'geocode' => 'array',
    ];

    /**
     * Get the parent model of the address (e.g., User, Trip, etc.).
     */
    public function addressable(): MorphTo
    {
        return $this->morphTo();
    }

    public function label()
    {
        return $this->belongsTo(AddressLabel::class, 'address_label_id');
    }

    // public function tripsAsDeparture()
    // {
    //     return $this->hasMany(Trip::class, 'departure_address_id');
    // }

    // public function tripsAsArrival()
    // {
    //     return $this->hasMany(Trip::class, 'arrival_address_id');
    // }
}
