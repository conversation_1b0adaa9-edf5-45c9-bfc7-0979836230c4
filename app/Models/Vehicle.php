<?php

namespace App\Models;

use App\Enums\VehicleColors;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class Vehicle extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'color' => VehicleColors::class,
        'global_status' => \App\Enums\Vehicles\VehicleStatus::class,
        'status' => \App\Enums\UserStatus::class,
        'rejection_reason_columns' => 'array',
    ];

    protected $appends = [];

    public function vehicleModel()
    {
        return $this->belongsTo(VehicleModel::class);
    }

    public function drivers()
    {
        return $this->belongsToMany(Driver::class)->withTrashed();
    }

    public function trips()
    {
        return $this->hasMany(Trip::class);
    }

    public function getCurrentDriverAvailability()
    {
        if ($this->drivers()->exists()) {
            $driver = $this->drivers()->first();

            return $driver->driverAvailabilities()->latest()->first()->status ?? 'off-duty';
        }

        return null;
    }

    public function tripRatings()
    {
        return $this->hasMany(TripRating::class);
    }

    public function averageRating()
    {
        return $this->tripRatings()->average('rating');
    }

    /**
     * Get the current driver's location
     */
    public function getDriverLocation(): ?array
    {
        $driver = $this->drivers->first();

        if (! $driver || ! $driver->location) {
            return null;
        }

        // Handle PostgreSQL with PostGIS
        if (DB::connection()->getDriverName() === 'pgsql') {
            $locationData = DB::select('
                SELECT ST_X(location::geometry) as lng, ST_Y(location::geometry) as lat
                FROM drivers
                WHERE id = ?
            ', [$driver->id]);

            if (empty($locationData)) {
                return null;
            }

            return [
                'lat' => (float) $locationData[0]->lat,
                'lng' => (float) $locationData[0]->lng,
            ];
        }

        // Handle other databases (SQLite, MySQL, etc.)
        return $this->extractLocationCoordinates($driver->location);
    }

    /**
     * Extract coordinates from location field for non-PostgreSQL databases
     */
    private function extractLocationCoordinates(?string $location): ?array
    {
        if (empty($location)) {
            return null;
        }

        // Handle WKT format: POINT(lng lat)
        if (preg_match('/POINT\(([+-]?\d*\.?\d+)\s+([+-]?\d*\.?\d+)\)/', $location, $matches)) {
            return [
                'lat' => (float) $matches[2],
                'lng' => (float) $matches[1],
            ];
        }

        // Handle JSON format: {"lat": 123, "lng": 456}
        $decoded = json_decode($location, true);
        if (is_array($decoded) && isset($decoded['lat']) && isset($decoded['lng'])) {
            return [
                'lat' => (float) $decoded['lat'],
                'lng' => (float) $decoded['lng'],
            ];
        }

        return null;
    }

    public function vehicleEquipments()
    {
        return $this->belongsToMany(VehicleEquipment::class, 'vehicle_equipment_links');
    }

    public function vehicleType()
    {
        return $this->belongsTo(VehicleType::class, 'vehicle_type_id');
    }

    public function documents()
    {
        return $this->hasOne(VehicleDocuments::class, 'vehicle_id');
    }
    // public function setVehicleLastPositionAttribute(?array $location): void
    // {
    //     if (is_array($location)) {
    //         $this->attributes['last_lat'] = $location['lat'];
    //         $this->attributes['last_lng'] = $location['lng'];
    //         unset($this->attributes['vehicle_last_position']);
    //     }
    // }

    // public static function getComputedLocation(): string
    // {
    //     return 'vehicle_last_position';
    // }

    /**
     * Get the current active trip for this vehicle
     */
    public function getCurrentTrip()
    {
        return $this->trips()
            ->whereIn('status', [
                'assigned',
                'driver_arriving',
                'driver_arrived',
                'on_trip',
                'waiting_for_driver_confirmation',
            ])
            ->with(['rider.user', 'tripLocation'])
            ->first();
    }
}
