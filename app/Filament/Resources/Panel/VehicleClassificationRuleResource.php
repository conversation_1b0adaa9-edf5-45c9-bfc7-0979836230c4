<?php

namespace App\Filament\Resources\Panel;

use App\Enums\SeatConfiguration;
use App\Enums\VehicleTypesCategories;
use App\Filament\Resources\Panel\VehicleClassificationRuleResource\Pages;
use App\Models\VehicleBrand;
use App\Models\VehicleClassificationRule as ModelsVehicleClassificationRule;
use App\Models\VehicleModel;
use App\Models\VehicleType;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Section as SectionInfolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class VehicleClassificationRuleResource extends Resource
{
    protected static ?string $model = ModelsVehicleClassificationRule::class;

    /**
     * Get vehicle type options based on category and operation context
     */
    private static function getVehicleTypeOptions(?string $category, $record = null): array
    {
        if (empty($category)) {
            return [];
        }

        $query = VehicleType::query()->where('status', true);
        $usedVehicleTypeIds = ModelsVehicleClassificationRule::pluck('vehicle_type_id')->toArray();

        if ($record) {
            // Edit operation - include current record's type and available types
            $query->where(function ($query) use ($record, $category, $usedVehicleTypeIds) {
                $query->where('id', $record->vehicle_type_id)
                    ->orWhere(function ($query) use ($category, $usedVehicleTypeIds) {
                        $query->where('category', $category)
                            ->whereNotIn('id', $usedVehicleTypeIds);
                    });
            });
        } else {
            // Create operation - only show available types
            $query->where('category', $category)
                ->whereNotIn('id', $usedVehicleTypeIds);
        }

        return $query->pluck('name_en', 'id')->toArray();
    }

    /**
     * Get year options for min/max year selects
     */
    private static function getYearOptions(int $min = 1990, ?int $max = null): array
    {
        $max = $max ?? now()->year + 1;

        return collect(range($min, $max))
            ->mapWithKeys(fn ($year) => [$year => $year])
            ->toArray();
    }

    protected static ?string $navigationIcon = 'heroicon-o-adjustments-horizontal';

    protected static ?string $navigationLabel = 'Classification Rules';

    protected static ?string $navigationGroup = 'Configuration';

    protected static ?string $recordTitleAttribute = '';

    protected static ?string $modelLabel = 'Vehicle Classification Rule';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Vehicle Type Details')
                    ->columns(2)
                    ->description('Select the category and type of vehicle')
                    ->icon('heroicon-o-truck')
                    ->schema([
                        // Category Selection
                        Select::make('category')
                            ->label('Vehicle Category')
                            ->options(VehicleTypesCategories::class)
                            ->required()
                            ->native(false)
                            ->live()
                            ->reactive()
                            ->afterStateUpdated(function ($set) {
                                $set('vehicle_type_id', null);
                            })
                            ->helperText('Select a category to enable vehicle type selection')
                            ->columnSpan(1),

                        // Vehicle Type Selection (dependent on category)
                        Select::make('vehicle_type_id')
                            ->label('Vehicle Type')
                            ->options(fn (callable $get, $livewire) => self::getVehicleTypeOptions($get('category'), $livewire->record))
                            ->required()
                            ->native(false)
                            ->preload()
                            ->searchable()
                            ->disabled(fn (callable $get) => ! $get('category'))
                            ->hint(fn (callable $get) => $get('category') ? null : 'First select a category')
                            ->dehydrated()
                            ->reactive()
                            ->columnSpan(1),

                        Repeater::make('qualifications')
                            ->relationship()
                            ->columnSpanFull()
                            ->deleteAction(
                                fn (Action $action) => $action->requiresConfirmation(),
                            )
                            ->rules([
                                function () {
                                    return function (string $attribute, $value, \Closure $fail) {
                                        if (! is_array($value)) {
                                            return;
                                        }

                                        foreach ($value as $index => $qualification) {
                                            $minYear = $qualification['min_year'] ?? null;
                                            $maxYear = $qualification['max_year'] ?? null;

                                            if ($minYear && $maxYear && $minYear > $maxYear) {
                                                $fail('Qualification #'.($index + 1).': Minimum year cannot be greater than maximum year.');
                                            }
                                        }
                                    };
                                },
                            ])
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        Select::make('min_year')
                                            ->label('Minimum Year')
                                            ->native(false)
                                            ->options(self::getYearOptions(1990, now()->year))
                                            ->default(1990)
                                            ->suffix('Year')
                                            ->required()
                                            ->searchable()
                                            ->helperText('Oldest acceptable model year')
                                            ->visible(fn ($get) => $get('../../category') !== VehicleTypesCategories::Freight->value)
                                            ->columnSpan(1)
                                            ->reactive(),

                                        Select::make('max_year')
                                            ->label('Maximum Year')
                                            ->native(false)
                                            ->options(fn ($get) => self::getYearOptions($get('min_year') ?? 1990, now()->year + 1))
                                            ->default(now()->year)
                                            ->suffix('Year')
                                            ->required()
                                            ->searchable()
                                            ->helperText('Newest acceptable model year')
                                            ->visible(fn ($get) => $get('../../category') !== VehicleTypesCategories::Freight->value)
                                            ->columnSpan(1)
                                            ->reactive(),

                                        Select::make('brands')
                                            ->label('Compatible Brand')
                                            ->native(false)
                                            ->options(fn () => VehicleBrand::where('status', true)->pluck('name_en', 'id'))
                                            ->required()
                                            ->preload()
                                            ->searchable()
                                            ->live()
                                            ->placeholder('Select a brand')
                                            ->helperText('Select a compatible brand for this classification')
                                            ->afterStateUpdated(function (callable $set) {
                                                $set('models', []); // Reset models selection
                                            })
                                            ->visible(fn ($get) => $get('../../category') !== VehicleTypesCategories::Freight->value)
                                            ->columnSpanFull()
                                            ->afterStateHydrated(function (Select $component, $state) {
                                                // Make sure we store a single brand ID
                                                if (is_array($state)) {
                                                    $component->state($state[0] ?? null);
                                                } elseif (is_string($state) && $state !== '') {
                                                    try {
                                                        $decoded = json_decode($state, true);
                                                        $component->state(is_array($decoded) ? ($decoded[0] ?? null) : $decoded);
                                                    } catch (\Exception $e) {
                                                        $component->state($state);
                                                    }
                                                }
                                            }),

                                        Select::make('models')
                                            ->label('Compatible Models')
                                            ->multiple()
                                            ->native(false)
                                            ->options(function (callable $get) {
                                                $brand = $get('brands');

                                                if (empty($brand)) {
                                                    return [];
                                                }

                                                // Always work with an array of one brand for compatibility
                                                $brandIds = is_array($brand) ? $brand : [$brand];

                                                return VehicleModel::query()
                                                    ->whereIn('vehicle_brand_id', $brandIds)
                                                    ->where('status', true)
                                                    ->pluck('name_en', 'id');
                                            })
                                            ->required()
                                            ->preload()
                                            ->searchable()
                                            ->live()
                                            ->placeholder(fn (callable $get) => empty($get('brands')) ? 'First select a brand' : 'Select models')
                                            ->helperText('Select all compatible models from the selected brand')
                                            ->visible(fn ($get) => $get('../../category') !== VehicleTypesCategories::Freight->value)
                                            ->columnSpanFull(),

                                        Group::make()
                                            ->schema([
                                                Select::make('seat_numbers')
                                                    ->label('Seat Configuration')
                                                    ->multiple()
                                                    ->native(false)
                                                    ->options(SeatConfiguration::options())
                                                    ->required()
                                                    ->placeholder('Select seat configurations')
                                                    ->helperText('Select all compatible seating configurations')
                                                    ->columnSpanFull(),
                                            ])
                                            ->visible(fn ($get) => $get('../../category') === VehicleTypesCategories::Passenger->value)
                                            ->columnSpanFull(),

                                        Group::make()
                                            ->schema([
                                                Toggle::make('is_covered')
                                                    ->helperText('Check if the vehicle type is covered.')
                                                    ->label('Covered')
                                                    ->required()
                                                    ->rules(['boolean'])
                                                    ->onIcon('mdi-truck')
                                                    ->offIcon('mdi-truck-flatbed')
                                                    ->inline(false)
                                                    ->columnSpan(1),

                                                Select::make('weight_category')
                                                    ->label('Weight Category')
                                                    ->native(false)
                                                    ->required()
                                                    ->options([
                                                        'less_than_1000kg' => 'Less than 1000 kg',
                                                        'more_than_1000kg' => 'More than 1000 kg',
                                                    ])
                                                    ->placeholder('Select weight category')
                                                    ->helperText('Specify the weight category for this freight vehicle')
                                                    ->columnSpan(1),
                                            ])
                                        // Fix the visibility logic to ensure it works in both create and edit operations
                                            ->visible(function ($get, $record) {
                                                $category = $get('../../category');

                                                // This ensures we evaluate against the actual value
                                                return $category === VehicleTypesCategories::Freight->value;
                                            })
                                            ->columns(2)
                                            ->columnSpanFull(),
                                    ]),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('vehicleType.name_en')
                    ->label('Vehicle Type')
                    ->icon(fn ($record) => $record->vehicleType->category === VehicleTypesCategories::Passenger
                        ? 'mdi-car-side'
                        : 'heroicon-o-truck')
                    ->color(fn ($record) => $record->vehicleType->category === VehicleTypesCategories::Passenger
                        ? 'primary'
                        : 'warning'),

                Tables\Columns\TextColumn::make('category')
                    ->label('Category')
                    ->badge()
                    ->color(fn ($state) => match ($state) {
                        VehicleTypesCategories::Passenger => 'success',
                        VehicleTypesCategories::Freight => 'warning',
                        default => 'gray'
                    })
                    ->formatStateUsing(fn ($state) => $state?->getLabel()),

                Tables\Columns\TextColumn::make('qualifications')
                    ->label('Year Range')
                    ->formatStateUsing(function ($record) {
                        if ($record->qualifications->isEmpty()) {
                            return 'N/A';
                        }

                        $qualification = $record->qualifications->first();

                        if ($record->category !== VehicleTypesCategories::Freight->value) {
                            return "{$qualification->min_year} - {$qualification->max_year}";
                        }

                        return 'N/A';
                    })
                    ->color('info')
                    ->tooltip(function ($record) {
                        if ($record->qualifications->isEmpty()) {
                            return null;
                        }

                        return 'Vehicle year range';
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        VehicleTypesCategories::Passenger->value => 'Passenger',
                        VehicleTypesCategories::Freight->value => 'Freight',
                    ]),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Vehicle classification rule updated successfully')
                        ),
                    Tables\Actions\ViewAction::make()
                        ->modal(),
                    Tables\Actions\DeleteAction::make()->successNotification(
                        Notification::make()
                            ->success()
                            ->title('Vehicle classification rule deleted successfully')
                    ),
                ]),

            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                SectionInfolist::make('Classification Details')
                    ->schema([
                        TextEntry::make('category')
                            ->label('Vehicle Category')
                            ->icon('heroicon-o-adjustments-horizontal')
                            ->badge()
                            ->color(fn ($state) => match ($state instanceof VehicleTypesCategories ? $state->value : $state) {
                                VehicleTypesCategories::Passenger->value => 'success',
                                VehicleTypesCategories::Freight->value => 'warning',
                                default => 'gray'
                            })
                            ->formatStateUsing(fn ($state) => $state !== null && $state !== ''
                                ? ($state instanceof VehicleTypesCategories ? $state->getLabel() : VehicleTypesCategories::from($state)->getLabel())
                                : 'N/A'),

                        TextEntry::make('vehicleType.name_en')
                            ->label('Vehicle Type')
                            ->icon(fn ($record) => $record->vehicleType->category === VehicleTypesCategories::Passenger->value
                                ? 'heroicon-o-truck'
                                : 'mdi-car-side')
                            ->color(fn ($record) => $record->vehicleType->category === VehicleTypesCategories::Passenger->value
                                ? 'primary'
                                : 'warning')
                            ->default('N/A'),
                    ])
                    ->columns(2),

                SectionInfolist::make('Qualifications')
                    ->description('Details for classification per qualification type.')
                    ->schema([
                        RepeatableEntry::make('qualifications')
                            ->label('Qualifications')
                            ->schema([
                                // Shared Field
                                TextEntry::make('year_range')
                                    ->label('Year Range')
                                    ->icon('heroicon-o-calendar')
                                    ->getStateUsing(fn ($record) => $record->min_year && $record->max_year
                                        ? "{$record->min_year} - {$record->max_year}"
                                        : 'N/A')
                                    ->hidden(fn ($record) => optional($record->rule)->category === VehicleTypesCategories::Freight),

                                TextEntry::make('brands')
                                    ->label('Compatible Brand')
                                    ->icon('heroicon-o-building-storefront')
                                    ->formatStateUsing(fn ($state) => $state
                                        ? \App\Models\VehicleBrand::find($state)?->name_en ?? 'Unknown'
                                        : 'N/A')
                                    ->hidden(fn ($record) => optional($record->rule)->category === VehicleTypesCategories::Freight),

                                TextEntry::make('models')
                                    ->label('Compatible Models')
                                    ->icon('heroicon-o-cube')
                                    ->html() // Enable HTML rendering
                                    ->formatStateUsing(function ($state, $record) {
                                        $modelIds = $record->models ?? [];

                                        if (empty($modelIds) && is_string($state)) {
                                            $modelIds = json_decode($state, true) ?? [];
                                        }

                                        if (! is_array($modelIds) || empty($modelIds)) {
                                            return 'N/A';
                                        }

                                        $names = \App\Models\VehicleModel::whereIn('id', array_map('intval', $modelIds))
                                            ->pluck('name_en')
                                            ->toArray();

                                        return ! empty($names)
                                            ? collect($names)->map(fn ($name) => "🔹 {$name}")->implode('<br>')
                                            : 'N/A';
                                    })
                                    ->hidden(fn ($record) => optional($record->rule)->category === VehicleTypesCategories::Freight),

                                TextEntry::make('seat_numbers')
                                    ->label('Seat Configurations')
                                    ->icon('mdi-chair-rolling')
                                    ->formatStateUsing(fn ($state) => ! empty($state)
                                        ? collect($state)->map(fn ($seats) => "{$seats} Seats")->implode(', ')
                                        : 'N/A')
                                    ->hidden(fn ($record) => optional($record->rule)->category !== VehicleTypesCategories::Passenger),

                                IconEntry::make('is_covered')
                                    ->label('Covered')
                                    ->boolean()
                                    ->icon('heroicon-o-shield-check')
                                    ->color('success')
                                    ->hidden(fn ($record) => optional($record->rule)->category !== VehicleTypesCategories::Freight),

                                TextEntry::make('weight_category')
                                    ->label('Weight Category')
                                    ->icon('mdi-weight')
                                    ->formatStateUsing(fn ($state) => match ($state) {
                                        'less_than_1000kg' => 'Less than 1000 kg',
                                        'more_than_1000kg' => 'More than 1000 kg',
                                        default => 'N/A'
                                    })
                                    ->hidden(fn ($record) => optional($record->rule)->category !== VehicleTypesCategories::Freight),
                            ])
                            ->grid(2)
                            ->columns(2)
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->compact(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVehicleClassificationRules::route('/'),
            'create' => Pages\CreateVehicleClassificationRule::route('/create'),
            'edit' => Pages\EditVehicleClassificationRule::route('/{record}/edit'),
        ];
    }
}
